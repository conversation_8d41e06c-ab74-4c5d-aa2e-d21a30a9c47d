2025-08-04 14:39:27 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 14:39:27 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 14:39:27 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 8
2025-08-04 14:39:27 [信息] 所有线程已停止并清理
2025-08-04 14:39:27 [信息] 正在初始化多线程服务...
2025-08-04 14:39:27 [信息] 千川手机API服务已初始化
2025-08-04 14:39:27 [信息] 手机号码管理器已初始化，服务商: <PERSON><PERSON><PERSON><PERSON>，将在第一个线程完成第二页后获取手机号码
2025-08-04 14:39:27 [信息] 多线程服务初始化完成
2025-08-04 14:39:27 [信息] 数据分配完成：共8条数据分配给3个线程
2025-08-04 14:39:27 [信息] 线程1分配到3条数据
2025-08-04 14:39:27 [信息] 线程2分配到3条数据
2025-08-04 14:39:27 [信息] 线程3分配到2条数据
2025-08-04 14:39:27 [信息] 屏幕工作区域: 1280x672
2025-08-04 14:39:27 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 14:39:27 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 14:39:27 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 14:39:27 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=14 GB
2025-08-04 14:39:27 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 14:39:27 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 14:39:27 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 14:39:27 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 14:39:27 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 14:39:27 [信息] 屏幕工作区域: 1280x672
2025-08-04 14:39:27 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 14:39:27 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 14:39:27 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 14:39:27 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=12 GB
2025-08-04 14:39:27 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 14:39:27 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 14:39:27 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 14:39:27 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 14:39:27 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 14:39:27 [信息] 屏幕工作区域: 1280x672
2025-08-04 14:39:27 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 14:39:27 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 14:39:27 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 14:39:27 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=6 GB
2025-08-04 14:39:27 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 14:39:27 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 14:39:27 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 14:39:27 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 14:39:27 [信息] 多线程注册启动成功，共3个线程
2025-08-04 14:39:27 线程1：[信息] 开始启动注册流程
2025-08-04 14:39:27 线程2：[信息] 开始启动注册流程
2025-08-04 14:39:27 线程3：[信息] 开始启动注册流程
2025-08-04 14:39:27 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-04 14:39:27 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-04 14:39:27 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 14:39:27 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 14:39:27 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-04 14:39:27 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 14:39:27 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 14:39:27 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 14:39:27 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 14:39:27 [信息] 多线程管理窗口已初始化
2025-08-04 14:39:27 [信息] UniformGrid列数已更新为: 1
2025-08-04 14:39:27 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 14:39:27 [信息] 多线程管理窗口已打开
2025-08-04 14:39:27 [信息] 多线程注册启动成功，共3个线程
2025-08-04 14:39:31 [信息] UniformGrid列数已更新为: 1
2025-08-04 14:39:31 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 14:39:31 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 14:39:31 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 14:39:31 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 14:39:31 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 14:39:32 [信息] UniformGrid列数已更新为: 1
2025-08-04 14:39:32 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 14:39:32 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 14:39:32 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 14:39:32 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-04 14:39:32 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 14:39:32 [信息] UniformGrid列数已更新为: 2
2025-08-04 14:39:32 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 14:39:32 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 14:39:32 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 14:39:32 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 14:39:32 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 14:39:34 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 14:39:34 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 14:39:35 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 14:39:36 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 14:39:36 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 14:39:36 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 14:39:36 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 14:39:36 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-04 14:39:36 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-04 14:39:37 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 14:39:37 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 14:39:37 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 14:39:37 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 14:39:37 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-04 14:39:37 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-04 14:39:37 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 14:39:37 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 14:39:37 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 14:39:37 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 14:39:37 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-04 14:39:37 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-04 14:39:37 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 8核 (进度: 0%)
2025-08-04 14:39:37 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=14 GB
2025-08-04 14:39:37 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 2核 (进度: 0%)
2025-08-04 14:39:37 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=12 GB
2025-08-04 14:39:37 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 32核 (进度: 0%)
2025-08-04 14:39:37 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=6 GB
2025-08-04 14:39:38 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 14:39:38 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 14:39:38 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 14:39:40 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 32
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: user

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1C2D3E4F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: F0-12-34-56-78-9A
   • 屏幕分辨率: 1724x1014
   • 可用区域: 1724x974

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E7F8A9B0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: cellular
   • 电池API支持: True
   • 电池电量: 0.50
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 14:39:40 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 14 GB
   • 平台信息: Win32
   • Do Not Track: null

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9E0F1A2B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_009
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-O1P2Q3R
   • MAC地址: 78-9A-BC-DE-F0-12
   • 屏幕分辨率: 2100x1041
   • 可用区域: 2100x1001

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C9D0E1F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.83
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 14:39:40 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 32    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: user   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1C2D3E4F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: F0-12-34-56-78-9A    • 屏幕分辨率: 1724x1014    • 可用区域: 1724x974   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E7F8A9B0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: cellular    • 电池API支持: True    • 电池电量: 0.50    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 14:39:40 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 14 GB    • 平台信息: Win32    • Do Not Track: null   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9E0F1A2B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_009    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-O1P2Q3R    • MAC地址: 78-9A-BC-DE-F0-12    • 屏幕分辨率: 2100x1041    • 可用区域: 2100x1001   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C9D0E1F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.83    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 14:39:40 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 2
   • 设备内存: 12 GB
   • 平台信息: Win32
   • Do Not Track: disabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1A2B3C4D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_001
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Q3R4S5T
   • MAC地址: 9A-BC-DE-F0-12-34
   • 屏幕分辨率: 1893x1143
   • 可用区域: 1893x1103

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C3D4E5F6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.81
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 14:39:40 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 2    • 设备内存: 12 GB    • 平台信息: Win32    • Do Not Track: disabled   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1A2B3C4D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_001    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Q3R4S5T    • MAC地址: 9A-BC-DE-F0-12-34    • 屏幕分辨率: 1893x1143    • 可用区域: 1893x1103   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C3D4E5F6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.81    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 14:39:40 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 14:39:40 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 14:39:40 线程1：[信息] 浏览器启动成功
2025-08-04 14:39:40 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 14:39:40 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 14:39:41 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 14:39:41 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 14:39:41 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 14:39:41 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 14:39:41 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 14:39:41 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 14:39:41 线程2：[信息] 浏览器启动成功
2025-08-04 14:39:41 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 14:39:41 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 14:39:41 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 14:39:41 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 14:39:41 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 14:39:41 线程3：[信息] 浏览器启动成功
2025-08-04 14:39:41 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 14:39:41 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 14:39:41 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 14:39:41 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 14:39:41 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 14:39:41 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 14:39:41 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 14:39:41 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 14:39:41 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 14:39:41 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 14:39:41 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 14:39:41 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 14:39:41 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 14:39:41 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 14:39:42 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 14:39:42 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 14:39:42 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 14:39:42 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 14:39:42 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 14:39:42 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 14:40:11 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 14:40:11 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 14:40:11 [信息] 第一页相关失败，数据保持不动
2025-08-04 14:40:11 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 14:40:11 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 14:40:11 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 14:40:11 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 14:40:11 [信息] 第一页相关失败，数据保持不动
2025-08-04 14:40:11 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 14:40:11 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 14:40:12 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 14:40:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:12 [信息] 多线程状态已重置
2025-08-04 14:40:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:12 [信息] 多线程状态已重置
2025-08-04 14:40:12 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 14:40:12 [信息] 第一页相关失败，数据保持不动
2025-08-04 14:40:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:12 [信息] 多线程状态已重置
2025-08-04 14:40:12 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 14:40:12 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程3：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程3：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 14:40:59 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 14:40:59 [信息] 多线程状态已重置
2025-08-04 14:40:59 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 14:40:59 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 14:40:59 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 14:41:01 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 14:41:01 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 14:41:01 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 14:41:01 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 14:41:01 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 14:41:01 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 14:41:01 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 14:41:01 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 14:41:01 线程1：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 14:41:01 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 14:41:02 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 14:41:02 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 14:41:02 线程3：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 14:41:02 线程3：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 14:41:02 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 14:41:02 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 14:41:02 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 14:41:02 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 14:41:02 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 14:41:02 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 14:41:02 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 14:41:02 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 14:41:02 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 14:41:02 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 14:41:02 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 14:41:02 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 14:41:02 线程3：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 14:41:02 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 14:41:02 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 14:41:02 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 14:41:02 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 14:41:02 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 14:41:02 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 14:41:05 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 14:41:05 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 14:41:05 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 14:41:05 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 14:41:05 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 14:41:05 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 14:41:05 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 14:41:05 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 14:41:05 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 14:41:05 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 14:41:05 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 14:41:05 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 14:41:05 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 14:41:05 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 14:41:06 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 14:41:06 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 14:41:06 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 14:41:06 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 14:41:08 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 14:41:08 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 14:41:08 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 14:41:08 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 14:41:08 线程3：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 14:41:08 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 14:41:08 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 14:41:08 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 14:41:08 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 14:41:08 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 14:41:08 线程1：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 14:41:08 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 14:41:08 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 14:41:08 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 14:41:08 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 14:41:08 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 14:41:08 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 14:41:08 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 14:41:08 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 14:41:08 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 14:41:08 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 14:41:15 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35923 字节 (进度: 100%)
2025-08-04 14:41:15 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35923字节，复杂度符合要求 (进度: 100%)
2025-08-04 14:41:15 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 14:41:16 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35963 字节 (进度: 100%)
2025-08-04 14:41:16 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35963字节，复杂度符合要求 (进度: 100%)
2025-08-04 14:41:16 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 14:41:16 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35328 字节 (进度: 100%)
2025-08-04 14:41:16 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35328字节，复杂度符合要求 (进度: 100%)
2025-08-04 14:41:16 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 14:41:19 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"37c3r3"},"taskId":"0741a060-70fe-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 14:41:19 线程3：[信息] [信息] 第一页第1次识别结果: 37c3r3 → 转换为小写: 37c3r3 (进度: 100%)
2025-08-04 14:41:19 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 14:41:19 线程3：[信息] [信息] 已填入验证码: 37c3r3 (进度: 100%)
2025-08-04 14:41:19 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 14:41:21 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"34yc7f"},"taskId":"07f4e8be-70fe-11f0-b791-029e46bc98e3"} (进度: 100%)
2025-08-04 14:41:21 线程2：[信息] [信息] 第一页第1次识别结果: 34yc7f → 转换为小写: 34yc7f (进度: 100%)
2025-08-04 14:41:21 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 14:41:21 线程2：[信息] [信息] 已填入验证码: 34yc7f (进度: 100%)
2025-08-04 14:41:21 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 14:41:21 线程3：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 14:41:21 线程3：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 14:41:21 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 14:41:21 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 14:41:21 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 14:41:21 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 14:41:21 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 14:41:21 线程3：[信息] 已继续
2025-08-04 14:41:21 [信息] 线程3已继续
2025-08-04 14:41:21 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 14:41:21 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 14:41:22 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"bbc352"},"taskId":"08a82186-70fe-11f0-948a-9ab6db23b9b7"} (进度: 100%)
2025-08-04 14:41:22 线程1：[信息] [信息] 第一页第1次识别结果: bbc352 → 转换为小写: bbc352 (进度: 100%)
2025-08-04 14:41:22 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 14:41:22 线程1：[信息] [信息] 已填入验证码: bbc352 (进度: 100%)
2025-08-04 14:41:22 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 14:41:23 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 14:41:23 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 14:41:23 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 14:41:23 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 14:41:23 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 14:41:23 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 14:41:23 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 14:41:23 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 14:41:23 线程2：[信息] 已继续
2025-08-04 14:41:23 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 14:41:23 [信息] 线程2已继续
2025-08-04 14:41:23 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:23 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 14:41:23
2025-08-04 14:41:24 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 14:41:24 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 14:41:24 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 14:41:24 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 14:41:24 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 14:41:24 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 14:41:24 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 14:41:24 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 14:41:24 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 14:41:24 线程1：[信息] 已继续
2025-08-04 14:41:24 [信息] 线程1已继续
2025-08-04 14:41:24 [信息] 继续了 3 个可继续的线程
2025-08-04 14:41:26 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:26 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 14:41:26
2025-08-04 14:41:27 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:27 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 14:41:27
2025-08-04 14:41:27 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:27 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 14:41:27
2025-08-04 14:41:29 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:29 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 14:41:29
2025-08-04 14:41:30 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:30 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 14:41:30
2025-08-04 14:41:30 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:33 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 14:41:30
2025-08-04 14:41:33 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:33 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 14:41:33
2025-08-04 14:41:33 [信息] [线程3] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:33 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 14:41:33
2025-08-04 14:41:34 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:34 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 14:41:34
2025-08-04 14:41:34 [信息] [线程2] 邮箱验证码获取成功: 576425，立即停止重复请求
2025-08-04 14:41:34 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 14:41:34 [信息] [线程2] 已清理响应文件
2025-08-04 14:41:34 线程2：[信息] [信息] 验证码获取成功: 576425，正在自动填入... (进度: 100%)
2025-08-04 14:41:34 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 14:41:34 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 14:41:35 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 14:41:35 [信息] 线程2完成第二页事件已处理
2025-08-04 14:41:35 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-04 14:41:35 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 14:41:35 线程2：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 100%)
2025-08-04 14:41:35 线程2：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 100%)
2025-08-04 14:41:35 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 14:41:35 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 14:41:35 [信息] 开始批量获取3个手机号码，服务商: Qianchuan
2025-08-04 14:41:35 [信息] 千川API：不在初始化时获取手机号码，将在各线程第二页完成后获取
2025-08-04 14:41:35 [信息] 批量获取3个手机号码成功
2025-08-04 14:41:36 [信息] [线程3] 第5次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:36 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 14:41:36
2025-08-04 14:41:37 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:37 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 14:41:37
2025-08-04 14:41:38 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 14:41:38 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 14:41:38 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 14:41:38 [信息] [线程1] 邮箱验证码获取成功: 496061，立即停止重复请求
2025-08-04 14:41:38 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 14:41:38 [信息] [线程1] 已清理响应文件
2025-08-04 14:41:38 线程1：[信息] [信息] 验证码获取成功: 496061，正在自动填入... (进度: 100%)
2025-08-04 14:41:38 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 14:41:38 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 14:41:38 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 14:41:38 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 14:41:38 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 14:41:38 [信息] 线程1完成第二页事件已处理
2025-08-04 14:41:38 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-04 14:41:38 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 14:41:38 线程1：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 100%)
2025-08-04 14:41:38 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 14:41:38 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 14:41:38 线程1：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 100%)
2025-08-04 14:41:39 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 14:41:39 [信息] [线程3] 第6次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:39 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 14:41:39
2025-08-04 14:41:41 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 14:41:41 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 14:41:42 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 14:41:42 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 14:41:42 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 14:41:42 [信息] [线程3] 第7次触发邮箱验证码获取...（最多20次）
2025-08-04 14:41:42 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 14:41:42
2025-08-04 14:41:43 [信息] [线程3] 邮箱验证码获取成功: 691592，立即停止重复请求
2025-08-04 14:41:43 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 14:41:43 [信息] [线程3] 已清理响应文件
2025-08-04 14:41:43 线程3：[信息] [信息] 验证码获取成功: 691592，正在自动填入... (进度: 100%)
2025-08-04 14:41:43 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 14:41:43 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 14:41:43 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 14:41:43 [信息] 线程3完成第二页事件已处理
2025-08-04 14:41:43 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-04 14:41:43 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 14:41:43 线程3：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 100%)
2025-08-04 14:41:43 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 14:41:43 线程3：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 100%)
2025-08-04 14:41:43 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 14:41:46 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 14:41:46 线程3：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 14:41:46 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 14:41:46 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 14:41:46 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 14:41:47 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 14:41:50 线程1：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible (进度: 100%)
2025-08-04 14:41:50 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 14:41:50 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 14:41:50 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 14:41:53 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870300279","phoneId":"2411100279252","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5821310,"phoneNo":"4367870300279","projectId":804413,"startTime":"2025-08-04 14:41:53","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"4bcb8cd7-d554-40a6-bc5f-96ee7d36829e"}
2025-08-04 14:41:53 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870315977","phoneId":"2411115977754","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5821311,"phoneNo":"4367870315977","projectId":804413,"startTime":"2025-08-04 14:41:53","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"afee0ea3-27fa-4ac3-9a19-9a90530a0517"}
2025-08-04 14:41:53 [信息] [千川API] 获取手机号码成功: +4367870300279
2025-08-04 14:41:53 [信息] [千川API] 获取手机号码成功: +4367870315977
2025-08-04 14:41:57 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-04 14:41:57 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 14:41:57 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 14:41:57 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 14:41:57 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 14:41:57 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 14:41:57 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 14:41:57 线程1：[信息] [信息] 智能检测到当前在第1页，开始智能处理... (进度: 100%)
2025-08-04 14:41:57 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 14:41:57 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 14:41:57 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 14:41:57 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 14:41:58 线程1：[信息] [信息] 千川手机号码获取异常: The request was canceled due to the configured HttpClient.Timeout of 20 seconds elapsing. (进度: 100%)
2025-08-04 14:42:00 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 14:42:03 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 14:42:03 线程2：[信息] [信息] 第3.5页执行失败: Timeout 15000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Choose paid plan" }) to be visible (进度: 100%)
2025-08-04 14:42:03 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 14:42:03 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 14:42:03 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 14:42:03 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-04 14:42:03 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 14:42:03 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 14:42:03 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 14:42:03 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 14:42:04 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-04 14:42:04 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-04 14:42:07 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35592 字节 (进度: 100%)
2025-08-04 14:42:07 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35592字节，复杂度符合要求 (进度: 100%)
2025-08-04 14:42:07 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 14:42:10 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"s6hhgt"},"taskId":"252731a8-70fe-11f0-ae6e-62c5329370b7"} (进度: 100%)
2025-08-04 14:42:10 线程1：[信息] [信息] 第一页第1次识别结果: s6hhgt → 转换为小写: s6hhgt (进度: 100%)
2025-08-04 14:42:10 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 14:42:10 线程1：[信息] [信息] 已填入验证码: s6hhgt (进度: 100%)
2025-08-04 14:42:10 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 14:42:12 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 14:42:12 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 14:42:12 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 14:42:12 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 14:42:12 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 14:42:12 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 14:42:12 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 14:42:12 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 14:42:12 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 14:42:12 线程1：[信息] 已继续
2025-08-04 14:42:12 [信息] 线程1已继续
2025-08-04 14:42:14 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 14:42:15 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 14:42:14
2025-08-04 14:42:17 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 14:42:17 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 14:42:17
2025-08-04 14:42:20 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 14:42:20 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 14:42:20
2025-08-04 14:42:23 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 14:42:23 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 14:42:23
2025-08-04 14:42:26 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-08-04 14:42:26 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 14:42:26
2025-08-04 14:42:27 [信息] [线程1] 邮箱验证码获取成功: 496061，立即停止重复请求
2025-08-04 14:42:27 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 14:42:27 [信息] [线程1] 已清理响应文件
2025-08-04 14:42:27 线程1：[信息] [信息] 验证码获取成功: 496061，正在自动填入... (进度: 100%)
2025-08-04 14:42:27 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 14:42:29 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-04 14:42:29 [警告] 线程3未找到分配的手机号码，服务商: Qianchuan
2025-08-04 14:42:29 线程3：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 100%)
2025-08-04 14:42:29 线程3：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 100%)
2025-08-04 14:42:30 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 14:42:31 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 14:42:31 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 14:42:31 [信息] 线程1完成第二页事件已处理
2025-08-04 14:42:31 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-04 14:42:31 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 14:42:31 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 14:42:31 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 14:42:32 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 14:42:32 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 14:42:32 线程1：[信息] 已暂停
2025-08-04 14:42:32 [信息] 线程1已暂停
2025-08-04 14:42:32 [信息] 线程1已暂停
2025-08-04 14:42:33 线程3：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 14:42:33 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 14:42:33 线程3：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 14:42:33 线程3：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 14:42:33 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 14:42:34 线程1：[信息] [信息] 检测到注册已暂停或终止，停止后续步骤执行 (进度: 100%)
2025-08-04 14:42:35 线程3：[信息] [信息] 已选择国家代码 +43 (进度: 100%)
2025-08-04 14:42:36 线程3：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 100%)
2025-08-04 14:42:36 线程3：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 100%)
2025-08-04 14:42:36 线程3：[信息] [信息] 千川手机号码获取成功: +4367870315977 (进度: 100%)
2025-08-04 14:42:36 线程3：[信息] [信息] 千川手机号码已自动填入: +4367870315977 (进度: 100%)
2025-08-04 14:42:37 线程3：[信息] [信息] 使用已保存的手机号码: 67870315977 (进度: 100%)
2025-08-04 14:42:37 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 14:42:38 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870302318","phoneId":"2415402318785","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5821327,"phoneNo":"4367870302318","projectId":804413,"startTime":"2025-08-04 14:42:37","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"1586f42d-ae42-4ca3-9727-06018537ec0a"}
2025-08-04 14:42:38 [信息] [千川API] 获取手机号码成功: +4367870302318
2025-08-04 14:42:43 线程3：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 14:42:44 线程3：[信息] [信息] 正在选择月份: June (进度: 100%)
2025-08-04 14:42:45 线程3：[信息] [信息] 已选择月份（标准选项）: June (进度: 100%)
2025-08-04 14:42:45 线程3：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-04 14:42:46 线程3：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 100%)
2025-08-04 14:42:46 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 14:42:46 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 14:42:46 线程3：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 14:42:46 线程3：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 14:42:51 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 14:42:52 线程3：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-04 14:42:53 线程3：[信息] [信息] 已清空并重新填写手机号码: 67870315977 (进度: 100%)
2025-08-04 14:42:53 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 14:42:55 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 14:42:55 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 14:42:55 线程3：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 14:42:55 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 14:42:58 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 14:42:58 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 14:43:05 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34705 字节 (进度: 100%)
2025-08-04 14:43:05 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34705字节，复杂度符合要求 (进度: 100%)
2025-08-04 14:43:05 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 14:43:07 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"hw483x"},"taskId":"47c4bf50-70fe-11f0-948a-9ab6db23b9b7"} (进度: 100%)
2025-08-04 14:43:07 线程3：[信息] [信息] 第六页第1次识别结果: hw483x → 转换为小写: hw483x (进度: 100%)
2025-08-04 14:43:07 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 14:43:07 线程3：[信息] [信息] 第六页已填入验证码: hw483x (进度: 100%)
2025-08-04 14:43:08 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 14:43:11 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 14:43:11 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 14:43:14 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 14:43:17 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 14:43:17 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 14:43:17 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 14:43:17 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 14:43:22 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-04 14:43:22 线程3：[信息] [信息] 线程3等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-04 14:43:27 线程3：[信息] [信息] 线程3第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-04 14:43:28 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:43:28 [信息] [千川API] 获取验证码请求: 4367870302318
2025-08-04 14:43:29 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"30cdaac8-e3fa-43ed-93d1-f58f6ae0cefb"}
2025-08-04 14:43:29 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-04 14:43:29 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 14:43:29 线程3：[信息] [信息] 线程3第1次获取千川验证码失败，3秒后重试...（剩余9次尝试） (进度: 100%)
2025-08-04 14:43:32 线程3：[信息] [信息] 线程3第2次尝试获取千川验证码...（剩余8次尝试） (进度: 100%)
2025-08-04 14:43:32 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:43:32 [信息] [千川API] 获取验证码请求: 4367870302318
2025-08-04 14:43:32 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"1692388b-8e50-43c2-8b85-4dfadb822274"}
2025-08-04 14:43:32 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-04 14:43:32 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 14:43:32 线程3：[信息] [信息] 线程3第2次获取千川验证码失败，3秒后重试...（剩余8次尝试） (进度: 100%)
2025-08-04 14:43:35 线程3：[信息] [信息] 线程3第3次尝试获取千川验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 14:43:35 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:43:35 [信息] [千川API] 获取验证码请求: 4367870302318
2025-08-04 14:43:36 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"2510071d-5fe1-4475-8288-4ed4f1ec3804"}
2025-08-04 14:43:36 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-04 14:43:36 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 14:43:36 线程3：[信息] [信息] 线程3第3次获取千川验证码失败，3秒后重试...（剩余7次尝试） (进度: 100%)
2025-08-04 14:43:39 线程3：[信息] [信息] 线程3第4次尝试获取千川验证码...（剩余6次尝试） (进度: 100%)
2025-08-04 14:43:39 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:43:39 [信息] [千川API] 获取验证码请求: 4367870302318
2025-08-04 14:43:39 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"659c6f25-a756-4a98-bf9c-99e5915f99df"}
2025-08-04 14:43:39 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-04 14:43:39 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 14:43:39 线程3：[信息] [信息] 线程3第4次获取千川验证码失败，3秒后重试...（剩余6次尝试） (进度: 100%)
2025-08-04 14:43:42 线程3：[信息] [信息] 线程3第5次尝试获取千川验证码...（剩余5次尝试） (进度: 100%)
2025-08-04 14:43:42 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:43:42 [信息] [千川API] 获取验证码请求: 4367870302318
2025-08-04 14:43:42 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"4cdb568f-9d55-40f3-b63f-094458cbd11b"}
2025-08-04 14:43:42 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-04 14:43:42 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 14:43:42 线程3：[信息] [信息] 线程3第5次获取千川验证码失败，3秒后重试...（剩余5次尝试） (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 2 (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 14:43:44 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-04 14:43:44 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 14:43:44 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 14:43:44 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息] ❌ 按钮匹配失败: Target page, context or browser has been closed (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息] 🔬 执行详细页面分析... (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息] ❌ 详细分析失败: Target page, context or browser has been closed (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息] 智能检测到当前在第2页，开始智能处理... (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息] 智能检测到第2页，检查邮箱验证码模式... (进度: 100%)
2025-08-04 14:43:44 线程1：[信息] [信息] 邮箱验证码自动模式，继续自动执行... (进度: 100%)
2025-08-04 14:43:46 线程3：[信息] [信息] 线程3第6次尝试获取千川验证码...（剩余4次尝试） (进度: 100%)
2025-08-04 14:43:46 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:43:46 [信息] [千川API] 获取验证码请求: 4367870302318
2025-08-04 14:43:46 线程1：[信息] [信息] 等待验证码页面超时 (进度: 100%)
2025-08-04 14:43:46 线程1：[信息] 已继续
2025-08-04 14:43:46 [信息] 线程1已继续
2025-08-04 14:43:47 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"2930cd5a-ddee-4c82-a740-bb5c17b09807"}
2025-08-04 14:43:47 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-04 14:43:47 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 14:43:47 线程3：[信息] [信息] 线程3第6次获取千川验证码失败，3秒后重试...（剩余4次尝试） (进度: 100%)
2025-08-04 14:43:48 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Choose paid plan' → 第3页 (进度: 100%)
2025-08-04 14:43:48 线程2：[信息] [信息] 检测到第3.5页（账户类型确认页面），直接点击Choose paid plan按钮... (进度: 100%)
2025-08-04 14:43:48 线程2：[信息] [信息] 账户类型确认完成，进入第4页（联系信息页面）... (进度: 100%)
2025-08-04 14:43:48 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 14:43:48 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 14:43:48 线程1：[信息] 已暂停
2025-08-04 14:43:48 [信息] 线程1已暂停
2025-08-04 14:43:48 [信息] 线程1已暂停
2025-08-04 14:43:50 线程3：[信息] [信息] 线程3第7次尝试获取千川验证码...（剩余3次尝试） (进度: 100%)
2025-08-04 14:43:50 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:43:50 [信息] [千川API] 获取验证码请求: 4367870302318
2025-08-04 14:43:51 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"e5279f85-1697-40e2-8dd4-158d4b5d4a03"}
2025-08-04 14:43:51 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-04 14:43:51 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 14:43:51 线程3：[信息] [信息] 线程3第7次获取千川验证码失败，3秒后重试...（剩余3次尝试） (进度: 100%)
2025-08-04 14:43:51 线程2：[信息] [信息] 检测到注册已暂停或终止，停止后续操作 (进度: 100%)
2025-08-04 14:43:51 线程2：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 100%)
2025-08-04 14:43:51 线程2：[信息] [信息] 🔬 执行详细页面分析... (进度: 100%)
2025-08-04 14:43:53 线程2：[信息] [信息] ❌ 详细分析失败: Execution context was destroyed, most likely because of a navigation (进度: 100%)
2025-08-04 14:43:53 线程2：[信息] [信息] 智能检测到当前在第3页，开始智能处理... (进度: 100%)
2025-08-04 14:43:55 线程3：[信息] [信息] 线程3第8次尝试获取千川验证码...（剩余2次尝试） (进度: 100%)
2025-08-04 14:43:55 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:43:55 [信息] [千川API] 获取验证码请求: 4367870302318
2025-08-04 14:43:55 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"5c679952-08cf-4a9f-a508-eae8ca51ebf8"}
2025-08-04 14:43:55 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-04 14:43:55 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 14:43:55 线程3：[信息] [信息] 线程3第8次获取千川验证码失败，3秒后重试...（剩余2次尝试） (进度: 100%)
2025-08-04 14:43:58 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 14:43:58 线程3：[信息] [信息] 线程3第9次尝试获取千川验证码...（剩余1次尝试） (进度: 100%)
2025-08-04 14:43:58 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:43:58 [信息] [千川API] 获取验证码请求: 4367870302318
2025-08-04 14:43:59 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"0feb9383-2262-4236-b39a-fc048e9b64b4"}
2025-08-04 14:43:59 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-04 14:43:59 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 14:43:59 线程3：[信息] [信息] 线程3第9次获取千川验证码失败，3秒后重试...（剩余1次尝试） (进度: 100%)
2025-08-04 14:44:02 线程3：[信息] [信息] 线程3第10次尝试获取千川验证码...（剩余0次尝试） (进度: 100%)
2025-08-04 14:44:02 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:44:02 [信息] [千川API] 获取验证码请求: 4367870302318
2025-08-04 14:44:02 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"82ecd5cf-53bd-4ea5-8427-bc0d2eebd171"}
2025-08-04 14:44:02 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-04 14:44:02 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 14:44:02 线程3：[信息] [信息] 线程3第10次获取千川验证码失败，3秒后重试...（剩余0次尝试） (进度: 100%)
2025-08-04 14:44:05 线程3：[信息] [信息] 线程3千川验证码获取超时失败（已尝试10次） (进度: 100%)
2025-08-04 14:44:05 [信息] 线程3千川手机号码已加入黑名单队列: +4367870302318 (原因: 验证码获取超时失败)
2025-08-04 14:44:05 线程3：[信息] [信息] 线程3验证码获取失败: 验证码获取超时失败（已尝试10次） (进度: 100%)
2025-08-04 14:44:05 [信息] 线程3手机号码已加入释放队列: +4367870302318 (原因: 验证码获取失败)
2025-08-04 14:44:05 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 14:44:05 线程3：[信息] [信息] 千川API验证码获取超时，直接转为手动模式 (进度: 100%)
2025-08-04 14:44:05 [错误] 手机API服务未设置，无法释放手机号码
2025-08-04 14:44:05 [信息] 定时批量释放完成: 手机API服务未设置
2025-08-04 14:44:05 线程3：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 14:44:08 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 14:44:17 线程2：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible (进度: 100%)
2025-08-04 14:44:17 线程2：[信息] 已继续
2025-08-04 14:44:17 [信息] 线程2已继续
2025-08-04 14:44:17 [信息] 继续了 2 个可继续的线程
2025-08-04 14:44:27 [信息] 定时检查发现1个待拉黑千川手机号码，开始批量拉黑
2025-08-04 14:44:27 [信息] 开始拉黑1个千川手机号码
2025-08-04 14:44:27 [信息] [千川API] 拉黑手机号码: 4367870302318
2025-08-04 14:44:29 [信息] [千川API] 手机号码拉黑成功: 4367870302318
2025-08-04 14:44:29 [信息] [千川API] 千川批量拉黑完成: 成功1个, 失败0个
2025-08-04 14:44:29 [信息] 定时批量拉黑完成: 千川批量拉黑完成: 成功1个, 失败0个
2025-08-04 14:44:35 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-04 14:44:35 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 14:44:35 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 14:44:35 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 14:44:39 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 100%)
2025-08-04 14:44:39 线程2：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-04 14:44:39 线程2：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-04 14:44:39 线程2：[信息] [信息] 智能检测到当前在第4页，开始智能处理... (进度: 100%)
2025-08-04 14:44:39 [警告] 线程2未找到分配的手机号码，服务商: Qianchuan
2025-08-04 14:44:39 线程2：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 100%)
2025-08-04 14:44:39 线程2：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 100%)
2025-08-04 14:44:39 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 14:44:40 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 14:44:41 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 14:44:42 线程2：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 14:44:42 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 14:44:42 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 14:44:42 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 14:44:43 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 14:44:44 线程2：[信息] [信息] 已选择国家代码 +43 (进度: 100%)
2025-08-04 14:44:45 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870313886","phoneId":"2428313886800","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5821367,"phoneNo":"4367870313886","projectId":804413,"startTime":"2025-08-04 14:44:44","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"1c1bb443-3b2e-470f-a9c2-ecc7ed82ccd7"}
2025-08-04 14:44:45 [信息] [千川API] 获取手机号码成功: +4367870313886
2025-08-04 14:44:45 线程2：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 100%)
2025-08-04 14:44:45 线程2：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 100%)
2025-08-04 14:44:45 线程2：[信息] [信息] 千川手机号码获取成功: +4367870300279 (进度: 100%)
2025-08-04 14:44:45 线程2：[信息] [信息] 千川手机号码已自动填入: +4367870300279 (进度: 100%)
2025-08-04 14:44:46 线程2：[信息] [信息] 使用已保存的手机号码: 67870300279 (进度: 100%)
2025-08-04 14:44:46 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 14:44:49 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 14:44:50 线程2：[信息] [信息] 正在选择月份: March (进度: 100%)
2025-08-04 14:44:51 线程2：[信息] [信息] 已选择月份（标准选项）: March (进度: 100%)
2025-08-04 14:44:51 线程2：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-04 14:44:52 线程2：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 100%)
2025-08-04 14:44:52 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 14:44:52 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 14:44:52 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 14:44:52 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 14:44:58 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 14:44:59 线程2：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-04 14:44:59 线程2：[信息] [信息] 已清空并重新填写手机号码: 67870300279 (进度: 100%)
2025-08-04 14:44:59 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 14:45:01 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 14:45:01 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 14:45:01 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 14:45:01 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 14:45:05 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 14:45:05 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 14:45:08 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35190 字节 (进度: 100%)
2025-08-04 14:45:08 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，35190字节，复杂度符合要求 (进度: 100%)
2025-08-04 14:45:08 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 14:45:09 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"vyfdch"},"taskId":"906ed70e-70fe-11f0-9768-ba03bdd70631"} (进度: 100%)
2025-08-04 14:45:09 线程2：[信息] [信息] 第六页第1次识别结果: vyfdch → 转换为小写: vyfdch (进度: 100%)
2025-08-04 14:45:09 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 14:45:09 线程2：[信息] [信息] 第六页已填入验证码: vyfdch (进度: 100%)
2025-08-04 14:45:09 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 14:45:13 线程2：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 14:45:13 线程2：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-04 14:45:16 线程2：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 14:45:19 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 30712 字节 (进度: 100%)
2025-08-04 14:45:19 线程2：[信息] [信息] ✅ 图片验证通过：200x70px，30712字节，复杂度符合要求 (进度: 100%)
2025-08-04 14:45:19 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 14:45:21 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"5bm87m"},"taskId":"972c7416-70fe-11f0-b791-029e46bc98e3"} (进度: 100%)
2025-08-04 14:45:21 线程2：[信息] [信息] 第六页第2次识别结果: 5bm87m → 转换为小写: 5bm87m (进度: 100%)
2025-08-04 14:45:21 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 14:45:21 线程2：[信息] [信息] 第六页已填入验证码: 5bm87m (进度: 100%)
2025-08-04 14:45:21 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 14:45:25 线程2：[信息] [信息] 第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 14:45:25 线程2：[信息] [信息] 第六页第2次识别异常: 验证码错误 (进度: 100%)
2025-08-04 14:45:27 线程2：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 14:45:30 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31081 字节 (进度: 100%)
2025-08-04 14:45:30 线程2：[信息] [信息] ✅ 图片验证通过：200x70px，31081字节，复杂度符合要求 (进度: 100%)
2025-08-04 14:45:30 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 14:45:32 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"8md2b"},"taskId":"9dc71ef2-70fe-11f0-9ab0-ba03bdd70631"} (进度: 100%)
2025-08-04 14:45:32 线程2：[信息] [信息] 第六页第3次识别结果: 8md2b → 转换为小写: 8md2b (进度: 100%)
2025-08-04 14:45:32 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 14:45:32 线程2：[信息] [信息] 第六页已填入验证码: 8md2b (进度: 100%)
2025-08-04 14:45:32 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 14:45:36 线程2：[信息] [信息] 第3次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 14:45:36 线程2：[信息] [信息] 第六页第3次识别异常: 验证码错误 (进度: 100%)
2025-08-04 14:45:36 线程2：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 14:45:36 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 14:45:36 线程2：[信息] 已继续
2025-08-04 14:45:36 [信息] 线程2已继续
2025-08-04 14:46:03 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-08-04 14:46:03 线程2：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-08-04 14:46:03 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 14:46:03 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 14:46:05 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 14:46:05 线程2：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 14:46:05 线程2：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-08-04 14:46:05 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 14:46:05 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 14:46:05 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 14:46:05 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 14:46:11 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 14:46:11 线程2：[信息] [信息] 线程2等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-04 14:46:16 线程2：[信息] [信息] 线程2第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-04 14:46:16 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 14:46:16 [信息] [千川API] 获取验证码请求: 4367870300279
2025-08-04 14:46:18 [信息] [千川API] 验证码响应: {"data":{"code":"1762","message":"ok","modle":"[AMAZON] 1762"},"msg":"操作成功","status":200,"success":true,"t":"1d5f1169-fe93-475d-b4f3-e950d85f2edf"}
2025-08-04 14:46:18 [信息] [千川API] 获取验证码成功: 1762
2025-08-04 14:46:18 线程2：[信息] [信息] 线程2千川验证码获取成功: 1762 (进度: 100%)
2025-08-04 14:46:18 线程2：[信息] [信息] 线程2验证码获取成功: 1762，立即填入验证码... (进度: 100%)
2025-08-04 14:46:18 [信息] 线程2手机号码已加入释放队列: +4367870300279 (原因: 验证码获取成功)
2025-08-04 14:46:18 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 14:46:18 线程2：[信息] [信息] 线程2已自动填入手机验证码: 1762 (进度: 100%)
2025-08-04 14:46:19 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-04 14:46:20 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 14:46:23 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 14:46:23 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 14:46:24 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 14:46:27 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 14:46:27 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 14:46:35 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 14:46:35 [错误] 手机API服务未设置，无法释放手机号码
2025-08-04 14:46:35 [信息] 定时批量释放完成: 手机API服务未设置
2025-08-04 14:46:35 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 14:46:35 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 14:46:35 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 14:46:52 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 14:46:52 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 14:46:53 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 14:46:53 [信息] 成功点击更多按钮
2025-08-04 14:46:54 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 14:46:54 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 14:46:54 [信息] 成功点击账户信息按钮
2025-08-04 14:46:55 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 14:46:55 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 14:46:55 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 14:46:55 [信息] 成功定位到'安全凭证'链接
2025-08-04 14:47:00 线程3：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-08-04 14:47:00 线程3：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 14:47:00 线程3：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-08-04 14:47:00 线程3：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-08-04 14:47:00 线程3：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-08-04 14:47:00 线程3：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 14:47:00 线程3：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 14:47:01 线程3：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 14:47:01 线程3：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6833位 (进度: 100%)
2025-08-04 14:47:01 线程3：[信息] [信息] 第七页：检测到用户已输入验证码，直接执行手动模式处理流程... (进度: 100%)
2025-08-04 14:47:01 线程3：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 14:47:01 线程3：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 14:47:01 线程3：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 14:47:01 线程3：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6833位 (进度: 100%)
2025-08-04 14:47:01 线程3：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-04 14:47:01 线程3：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-04 14:47:01 线程3：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-04 14:47:04 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 14:47:04 [信息] 成功点击'安全凭证'链接
2025-08-04 14:47:04 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 14:47:04 线程3：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-04 14:47:04 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 14:47:04 线程3：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 14:47:05 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 14:47:08 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 14:47:08 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 14:47:15 线程3：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 14:47:15 线程3：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 14:47:15 线程3：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 14:47:20 线程2：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 14:47:20 线程2：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 14:47:20 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 14:47:20 [信息] 开始创建和复制访问密钥
2025-08-04 14:47:20 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 14:47:20 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 14:47:20 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 14:47:20 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 14:47:32 线程3：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 14:47:32 线程3：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 14:47:34 线程3：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 14:47:34 [信息] 成功点击更多按钮
2025-08-04 14:47:35 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 14:47:35 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 14:47:35 线程3：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 14:47:35 线程3：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 14:47:35 [信息] 成功点击账户信息按钮
2025-08-04 14:47:36 线程3：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 14:47:36 线程3：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 14:47:36 线程3：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 14:47:36 [信息] 成功定位到'安全凭证'链接
2025-08-04 14:47:38 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 14:47:38 线程2：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 14:47:38 [信息] 使用id属性定位到确认复选框
2025-08-04 14:47:39 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 14:47:39 [信息] 成功勾选确认复选框
2025-08-04 14:47:40 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 14:47:43 线程3：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 14:47:43 [信息] 成功点击'安全凭证'链接
2025-08-04 14:47:43 线程3：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 14:47:50 线程2：[信息] [信息] ❌ 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key").First to be visible (进度: 100%)
2025-08-04 14:47:50 [信息] 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key").First to be visible
2025-08-04 14:47:50 线程2：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 14:47:50 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 14:47:50 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5y694LW0 ③AWS密码：Vu03lAN3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 14:47:50 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5y694LW0 ③AWS密码：Vu03lAN3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 14:47:50 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：5y694LW0 ③AWS密码：Vu03lAN3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 14:47:50 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：5y694LW0 ③AWS密码：Vu03lAN3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 14:47:50 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：5y694LW0 ③AWS密码：Vu03lAN3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 14:47:50 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：5y694LW0 ③AWS密码：Vu03lAN3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 14:47:50 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 14:47:50 线程2：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 14:47:50 线程2：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 14:47:50 [信息] 注册完成（密钥提取失败）
2025-08-04 14:47:50 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 14:47:51 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 14:47:51 [信息] 已完成数据移除: <EMAIL>
2025-08-04 14:47:51 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 14:47:51 线程2：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 14:47:51 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 14:47:51 线程2：[信息] 已继续
2025-08-04 14:47:51 [信息] 线程2已继续
2025-08-04 14:47:58 线程3：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 14:47:58 线程3：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 14:47:58 线程3：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 14:47:58 [信息] 开始创建和复制访问密钥
2025-08-04 14:47:58 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 14:47:58 线程3：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 14:47:58 线程3：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 14:47:58 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 14:48:28 线程3：[信息] [信息] ❌ 创建和复制访问密钥失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key")
  -   locator resolved to <button type="submit" data-testid="create-access-key" da…>…</button>
  - attempting click action
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #1
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #2
  -   waiting 20ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #3
  -   waiting 100ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #4
  -   waiting 100ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #5
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #6
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #7
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #8
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #9
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #10
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #11
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #12
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #13
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #14
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #15
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #16
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #17
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #18
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #19
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #20
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #21
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #22
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #23
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #24
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #25
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #26
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #27
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #28
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #29
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #30
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #31
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #32
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #33
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #34
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #35
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #36
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #37
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #38
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #39
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #40
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #41
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #42
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #43
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #44
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #45
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #46
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #47
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #48
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #49
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #50
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #51
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #52
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #53
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #54
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #55
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #56
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #57
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable (进度: 100%)
2025-08-04 14:48:28 [信息] 创建和复制访问密钥失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key")
  -   locator resolved to <button type="submit" data-testid="create-access-key" da…>…</button>
  - attempting click action
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #1
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #2
  -   waiting 20ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #3
  -   waiting 100ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #4
  -   waiting 100ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #5
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #6
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #7
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #8
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #9
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #10
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #11
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #12
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #13
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #14
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #15
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #16
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #17
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #18
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #19
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #20
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #21
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #22
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #23
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #24
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #25
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #26
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #27
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #28
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #29
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #30
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #31
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #32
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #33
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #34
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #35
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #36
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #37
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #38
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #39
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #40
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #41
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #42
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #43
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #44
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #45
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #46
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #47
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #48
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #49
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #50
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #51
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #52
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #53
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <p class="globalNav-22115">Aquí puede obtener acceso a todos los servicios d…</p> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #54
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span class="globalNav-22114">Menú de servicios</span> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #55
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #56
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div data-analytics-render="true" data-analytics-proc…>…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #57
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
2025-08-04 14:48:28 线程3：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 14:48:28 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 14:48:28 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：NFao00Qf664W ③AWS密码：Bw3Isx3a ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 14:48:28 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：NFao00Qf664W ③AWS密码：Bw3Isx3a ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 14:48:28 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：NFao00Qf664W ③AWS密码：Bw3Isx3a ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 14:48:28 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：NFao00Qf664W ③AWS密码：Bw3Isx3a ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 14:48:28 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：NFao00Qf664W ③AWS密码：Bw3Isx3a ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 14:48:28 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：NFao00Qf664W ③AWS密码：Bw3Isx3a ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 14:48:28 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 14:48:28 线程3：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 14:48:28 线程3：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 14:48:28 [信息] 注册完成（密钥提取失败）
2025-08-04 14:48:28 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 14:48:28 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 14:48:28 [信息] 已完成数据移除: <EMAIL>
2025-08-04 14:48:28 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 14:48:28 线程3：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 14:48:28 [信息] 线程3数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 14:48:28 线程3：[信息] 已继续
2025-08-04 14:48:28 [信息] 线程3已继续
2025-08-04 14:49:57 [信息] 多线程窗口引用已清理
2025-08-04 14:49:57 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 14:49:57 [信息] 多线程管理窗口正在关闭
2025-08-04 14:49:58 [信息] 程序正在退出，开始清理工作...
2025-08-04 14:49:58 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 14:49:58 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 14:49:58 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 14:49:58 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 14:49:58 [信息] 程序退出清理工作完成
