2025-08-04 17:10:01 [信息] AWS自动注册工具启动
2025-08-04 17:10:01 [信息] 程序版本: 1.0.0.0
2025-08-04 17:10:01 [信息] 启动时间: 2025-08-04 17:10:01
2025-08-04 17:10:01 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 17:10:01 [信息] 线程数量已选择: 1
2025-08-04 17:10:01 [信息] 线程数量选择初始化完成
2025-08-04 17:10:01 [信息] 程序初始化完成
2025-08-04 17:10:04 [信息] 程序正在退出，开始清理工作...
2025-08-04 17:10:04 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 17:10:04 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 17:10:04 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 17:10:04 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 17:10:04 [信息] 程序退出清理工作完成
2025-08-04 17:10:36 [信息] AWS自动注册工具启动
2025-08-04 17:10:36 [信息] 程序版本: 1.0.0.0
2025-08-04 17:10:36 [信息] 启动时间: 2025-08-04 17:10:36
2025-08-04 17:10:36 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 17:10:36 [信息] 线程数量已选择: 1
2025-08-04 17:10:36 [信息] 线程数量选择初始化完成
2025-08-04 17:10:36 [信息] 程序初始化完成
2025-08-04 17:10:42 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 17:10:44 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 17:10:45 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 17:10:45 [信息] 成功加载 18 条数据
2025-08-04 17:10:46 [信息] 线程数量已选择: 3
2025-08-04 17:10:51 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 17:10:51 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 17:10:51 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 18
2025-08-04 17:10:51 [信息] 所有线程已停止并清理
2025-08-04 17:10:51 [信息] 正在初始化多线程服务...
2025-08-04 17:10:51 [信息] 榴莲手机API服务已初始化
2025-08-04 17:10:51 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-04 17:10:51 [信息] 多线程服务初始化完成
2025-08-04 17:10:51 [信息] 数据分配完成：共18条数据分配给3个线程
2025-08-04 17:10:51 [信息] 线程1分配到6条数据
2025-08-04 17:10:51 [信息] 线程2分配到6条数据
2025-08-04 17:10:51 [信息] 线程3分配到6条数据
2025-08-04 17:10:51 [信息] 屏幕工作区域: 1280x672
2025-08-04 17:10:51 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 17:10:51 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 17:10:51 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:10:51 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_006, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=10 GB
2025-08-04 17:10:51 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 17:10:51 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 17:10:51 [信息] 屏幕工作区域: 1280x672
2025-08-04 17:10:51 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 17:10:51 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 17:10:51 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:10:51 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=32 GB
2025-08-04 17:10:51 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 17:10:51 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 17:10:51 [信息] 屏幕工作区域: 1280x672
2025-08-04 17:10:51 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 17:10:51 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 17:10:51 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:10:51 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_002, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=6 GB
2025-08-04 17:10:51 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 17:10:51 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:10:51 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 17:10:51 [信息] 多线程注册启动成功，共3个线程
2025-08-04 17:10:51 线程1：[信息] 开始启动注册流程
2025-08-04 17:10:51 线程2：[信息] 开始启动注册流程
2025-08-04 17:10:51 线程3：[信息] 开始启动注册流程
2025-08-04 17:10:51 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-04 17:10:51 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 17:10:51 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 17:10:51 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 17:10:51 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-04 17:10:51 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 17:10:51 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 17:10:51 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 17:10:51 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 17:10:51 [信息] 多线程管理窗口已初始化
2025-08-04 17:10:51 [信息] UniformGrid列数已更新为: 1
2025-08-04 17:10:51 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 17:10:51 [信息] 多线程管理窗口已打开
2025-08-04 17:10:51 [信息] 多线程注册启动成功，共3个线程
2025-08-04 17:10:55 [信息] UniformGrid列数已更新为: 1
2025-08-04 17:10:55 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 17:10:55 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 17:10:55 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 17:10:55 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 17:10:55 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 17:10:56 [信息] UniformGrid列数已更新为: 1
2025-08-04 17:10:56 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 17:10:56 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 17:10:56 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 17:10:56 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-04 17:10:56 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 17:10:56 [信息] UniformGrid列数已更新为: 2
2025-08-04 17:10:56 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 17:10:56 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 17:10:56 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 17:10:56 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-04 17:10:56 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 17:10:58 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 17:10:58 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 17:10:58 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 17:11:00 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 17:11:00 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:11:00 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -36.8201, -73.0444 (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-36.8201, 经度=-73.0444
2025-08-04 17:11:00 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_006, CPU: 8核 (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器指纹注入: Canvas=canvas_fp_006, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=10 GB
2025-08-04 17:11:00 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 17:11:00 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:11:00 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 17:11:00 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 17:11:00 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 12核 (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=32 GB
2025-08-04 17:11:00 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 17:11:00 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:11:00 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 17:11:00 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 17:11:01 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_002, CPU: 12核 (进度: 0%)
2025-08-04 17:11:01 [信息] 浏览器指纹注入: Canvas=canvas_fp_002, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=6 GB
2025-08-04 17:11:02 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 17:11:02 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 17:11:03 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 12
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: auto

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Q3R4S5T
   • MAC地址: F0-12-34-56-78-9A
   • 屏幕分辨率: 1994x980
   • 可用区域: 1994x940

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C9D0E1F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 3g
   • 电池API支持: True
   • 电池电量: 0.38
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 17:11:03 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 10 GB
   • 平台信息: Win32
   • Do Not Track: 1

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9C0D1E2F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_003
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-U6V7W8X
   • MAC地址: F0-12-34-56-78-9A
   • 屏幕分辨率: 1897x1054
   • 可用区域: 1897x1014

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: D79834F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.62
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 17:11:03 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 12    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: auto   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Q3R4S5T    • MAC地址: F0-12-34-56-78-9A    • 屏幕分辨率: 1994x980    • 可用区域: 1994x940   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C9D0E1F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 3g    • 电池API支持: True    • 电池电量: 0.38    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 17:11:03 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 10 GB    • 平台信息: Win32    • Do Not Track: 1   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9C0D1E2F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_003    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-U6V7W8X    • MAC地址: F0-12-34-56-78-9A    • 屏幕分辨率: 1897x1054    • 可用区域: 1897x1014   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: D79834F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.62    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 17:11:03 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 12
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: enabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7E8F9A0B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_002
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: F0-12-34-56-78-9A
   • 屏幕分辨率: 2101x1124
   • 可用区域: 2101x1084

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A7B8C9D0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wimax
   • 电池API支持: True
   • 电池电量: 0.32
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 17:11:03 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 17:11:03 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 12    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: enabled   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7E8F9A0B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_002    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: F0-12-34-56-78-9A    • 屏幕分辨率: 2101x1124    • 可用区域: 2101x1084   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A7B8C9D0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wimax    • 电池API支持: True    • 电池电量: 0.32    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 17:11:03 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 17:11:03 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 17:11:03 线程1：[信息] 浏览器启动成功
2025-08-04 17:11:03 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 17:11:03 线程2：[信息] 浏览器启动成功
2025-08-04 17:11:03 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 17:11:03 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 17:11:03 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 17:11:03 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 17:11:04 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 17:11:04 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 17:11:04 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 17:11:04 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 17:11:04 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 17:11:04 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 17:11:04 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 17:11:04 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 17:11:04 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 17:11:04 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 17:11:04 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 17:11:04 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 17:11:04 线程3：[信息] 浏览器启动成功
2025-08-04 17:11:04 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 17:11:04 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 17:11:04 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 17:11:05 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 17:11:05 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 17:11:05 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 17:11:05 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 17:11:05 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 17:11:05 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 17:11:05 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 17:11:05 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 17:11:05 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 17:11:05 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 17:11:07 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 17:11:07 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-04 17:11:07 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 17:11:07 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 17:11:07 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 17:11:07 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 17:11:07 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 17:11:07 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 17:11:07 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 17:11:07 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 17:11:07 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 17:11:07 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 17:11:37 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 17:11:37 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 17:11:37 [信息] 第一页相关失败，数据保持不动
2025-08-04 17:11:37 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 17:11:37 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 17:11:38 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 17:11:38 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 17:11:38 [信息] 第一页相关失败，数据保持不动
2025-08-04 17:11:38 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 17:11:38 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 17:11:38 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-04 17:11:38 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 17:11:38 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 17:11:38 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 17:11:38 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 17:11:38 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 17:11:41 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 17:11:41 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 17:11:41 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 17:11:41 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 17:11:41 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:11:41 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 17:11:43 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 17:11:43 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:11:43 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 17:11:43 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 17:11:43 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 17:11:43 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 17:11:43 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 17:11:43 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 17:11:43 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 17:11:43 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 17:11:43 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 17:11:43 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 17:11:43 [信息] [线程2] 已删除旧的响应文件
2025-08-04 17:11:43 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 17:11:45 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 17:11:45 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:11:45
2025-08-04 17:11:48 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 17:11:48 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:11:48
2025-08-04 17:11:51 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 17:11:51 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:11:51
2025-08-04 17:11:54 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 17:11:54 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:11:54
2025-08-04 17:11:57 [信息] [线程2] 第5次触发邮箱验证码获取...（最多20次）
2025-08-04 17:11:57 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:11:57
2025-08-04 17:12:00 [信息] [线程2] 第6次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:00 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:00
2025-08-04 17:12:04 [信息] [线程2] 第7次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:04 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:04
2025-08-04 17:12:07 [信息] [线程2] 第8次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:07 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:07
2025-08-04 17:12:10 [信息] [线程2] 第9次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:10 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:10
2025-08-04 17:12:13 [信息] [线程2] 第10次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:13 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:13
2025-08-04 17:12:16 [信息] [线程2] 第11次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:16 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:16
2025-08-04 17:12:19 [信息] [线程2] 第12次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:19 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:19
2025-08-04 17:12:22 [信息] [线程2] 第13次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:22 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:22
2025-08-04 17:12:25 [信息] [线程2] 第14次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:25 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:25
2025-08-04 17:12:28 [信息] [线程2] 第15次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:28 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:28
2025-08-04 17:12:31 [信息] [线程2] 第16次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:31 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:31
2025-08-04 17:12:35 [信息] [线程2] 第17次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:36 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:35
2025-08-04 17:12:38 [信息] [线程2] 第18次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:38 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:38
2025-08-04 17:12:41 [信息] [线程2] 第19次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:41 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:41
2025-08-04 17:12:44 [信息] [线程2] 第20次触发邮箱验证码获取...（最多20次）
2025-08-04 17:12:44 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:12:44
2025-08-04 17:12:45 [警告] [线程2] 邮箱验证码获取失败（达到最大重试次数），共尝试20次
2025-08-04 17:12:45 [信息] [线程2] 已清理请求文件
2025-08-04 17:12:45 [信息] [线程2] 已清理响应文件
2025-08-04 17:12:45 线程2：[信息] [信息] 邮箱验证码自动获取失败: 邮箱验证码获取失败（达到最大重试次数），共尝试20次 (进度: 18%)
2025-08-04 17:12:45 线程2：[信息] [信息] 🔴 Microsoft获取验证码失败，转为手动模式 (进度: 18%)
2025-08-04 17:25:49 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 17:25:49 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 17:25:49 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 17:25:49 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 17:25:49 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 17:25:49 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 17:25:49 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 17:25:49 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 17:25:49 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 17:25:49 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 17:25:49 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 17:25:49 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 17:25:49 线程1：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 17:25:49 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 17:25:50 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 17:25:50 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 17:25:50 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 17:25:50 线程3：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 17:25:50 线程3：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 17:25:50 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 17:25:50 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 17:25:50 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 17:25:50 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 17:25:50 线程3：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 17:25:50 线程3：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 17:25:50 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 17:25:50 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 17:25:50 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 17:25:50 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 17:25:50 线程3：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 17:25:50 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 17:25:51 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 17:25:51 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 17:25:51 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 17:25:53 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 17:25:53 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 17:25:53 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 17:25:53 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 17:25:53 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:25:53 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 17:25:54 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 17:25:54 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 17:25:54 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 17:25:54 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 17:25:58 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 17:25:58 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:25:58 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 17:25:58 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:25:58 线程1：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 17:25:58 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 17:25:58 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 17:25:58 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 17:25:58 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:26:00 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 17:26:00 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:26:00 线程3：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 17:26:00 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 17:26:00 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 17:26:00 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 17:26:00 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:26:03 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35377 字节 (进度: 100%)
2025-08-04 17:26:03 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35377字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:26:03 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:26:03 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35403 字节 (进度: 100%)
2025-08-04 17:26:03 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35403字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:26:03 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:26:05 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"r853wf2"},"taskId":"0bb99b5e-7115-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 17:26:05 线程1：[信息] [信息] 第一页第1次识别结果: r853wf2 → 转换为小写: r853wf2 (进度: 100%)
2025-08-04 17:26:05 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:26:05 线程1：[信息] [信息] 已填入验证码: r853wf2 (进度: 100%)
2025-08-04 17:26:05 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:26:05 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"f6t6g7"},"taskId":"0bcf0476-7115-11f0-b791-029e46bc98e3"} (进度: 100%)
2025-08-04 17:26:05 线程3：[信息] [信息] 第一页第1次识别结果: f6t6g7 → 转换为小写: f6t6g7 (进度: 100%)
2025-08-04 17:26:05 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:26:05 线程3：[信息] [信息] 已填入验证码: f6t6g7 (进度: 100%)
2025-08-04 17:26:06 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:26:07 线程1：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 17:26:07 线程1：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-08-04 17:26:08 线程3：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 17:26:08 线程3：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 17:26:08 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 17:26:08 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 17:26:08 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 17:26:08 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 17:26:08 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 17:26:08 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 17:26:08 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 17:26:08 线程3：[信息] 已继续
2025-08-04 17:26:08 [信息] 线程3已继续
2025-08-04 17:26:09 线程1：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:26:10 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 17:26:10 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 17:26:10
2025-08-04 17:26:12 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForVerification，当前步骤: 2 (进度: 18%)
2025-08-04 17:26:12 线程2：[信息] [信息]  进行智能页面检测... (进度: 18%)
2025-08-04 17:26:12 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 18%)
2025-08-04 17:26:12 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 18%)
2025-08-04 17:26:12 线程2：[信息] [信息] ⚠️ 未找到匹配的页面按钮或链接 (进度: 18%)
2025-08-04 17:26:12 线程2：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 18%)
2025-08-04 17:26:12 线程2：[信息] [信息] 🔬 执行详细页面分析... (进度: 18%)
2025-08-04 17:26:12 线程2：[信息] [信息] 📄 页面URL: https://signin.aws.amazon.com/signup?request_type=register (进度: 18%)
2025-08-04 17:26:12 线程2：[信息] [信息] 📋 页面标题: 线程2 - AWS注册 (进度: 18%)
2025-08-04 17:26:13 线程2：[信息] [信息] 📊 分析结果: 第三页-密码设置(2/3个元素匹配) (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息]  智能检测到当前在第3页 (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 当前状态: WaitingForVerification, 步骤: 3，尝试智能检测页面... (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] ⚠️ 未找到匹配的页面按钮或链接 (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 🔬 执行详细页面分析... (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 📄 页面URL: https://signin.aws.amazon.com/signup?request_type=register (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 📋 页面标题: 线程2 - AWS注册 (进度: 38%)
2025-08-04 17:26:13 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 30945 字节 (进度: 100%)
2025-08-04 17:26:13 线程1：[信息] [信息] ✅ 图片验证通过：200x71px，30945字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:26:13 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:26:13 线程2：[信息] [信息] 📊 分析结果: 第三页-密码设置(2/3个元素匹配) (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 智能检测到当前在第3页，继续执行... (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 17:26:13 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 17:26:13 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 17:26:13
2025-08-04 17:26:13 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 17:26:13 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 17:26:14 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 17:26:16 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"3ynd84"},"taskId":"124c14d8-7115-11f0-948a-9ab6db23b9b7"} (进度: 100%)
2025-08-04 17:26:16 线程1：[信息] [信息] 第一页第2次识别结果: 3ynd84 → 转换为小写: 3ynd84 (进度: 100%)
2025-08-04 17:26:16 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:26:16 线程1：[信息] [信息] 已填入验证码: 3ynd84 (进度: 100%)
2025-08-04 17:26:16 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:26:16 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 17:26:16 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 17:26:16
2025-08-04 17:26:17 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 17:26:17 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 17:26:17 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 17:26:18 线程1：[信息] [信息] 第一页第2次图形验证码识别成功 (进度: 100%)
2025-08-04 17:26:18 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 17:26:18 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 17:26:18 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 17:26:18 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 17:26:18 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 17:26:18 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 17:26:18 线程1：[信息] 已继续
2025-08-04 17:26:18 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 17:26:18 [信息] 线程1已继续
2025-08-04 17:26:18 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 17:26:19 [信息] [线程3] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 17:26:19 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 17:26:19
2025-08-04 17:26:20 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 17:26:20 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 17:26:20
2025-08-04 17:26:22 [信息] [线程3] 第5次触发邮箱验证码获取...（最多20次）
2025-08-04 17:26:22 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 17:26:22
2025-08-04 17:26:23 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 17:26:23 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 17:26:23
2025-08-04 17:26:25 [信息] [线程3] 邮箱验证码获取成功: 082458，立即停止重复请求
2025-08-04 17:26:25 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 17:26:25 [信息] [线程3] 已清理响应文件
2025-08-04 17:26:25 线程3：[信息] [信息] 验证码获取成功: 082458，正在自动填入... (进度: 100%)
2025-08-04 17:26:25 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 17:26:25 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 17:26:25 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 17:26:25 [信息] 线程3完成第二页事件已处理
2025-08-04 17:26:25 [信息] 线程3完成第二页，开始批量获取手机号码...
2025-08-04 17:26:25 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 17:26:25 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-04 17:26:25 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-04 17:26:25 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 17:26:25 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 17:26:26 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 17:26:26 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 17:26:26
2025-08-04 17:26:28 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 17:26:28 线程3：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 17:26:28 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 17:26:28 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 17:26:28 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 17:26:29 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 17:26:29 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 17:26:29
2025-08-04 17:26:30 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 17:26:31 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+523513061202","+526862110619","+527571510446"]}
2025-08-04 17:26:31 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-04 17:26:31 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-04 17:26:31 [信息] 线程1分配榴莲手机号码: +523513061202
2025-08-04 17:26:31 [信息] 线程2分配榴莲手机号码: +526862110619
2025-08-04 17:26:31 [信息] 线程3分配榴莲手机号码: +527571510446
2025-08-04 17:26:31 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-04 17:26:31 [信息] 批量获取3个手机号码成功
2025-08-04 17:26:31 [信息] [线程1] 邮箱验证码获取成功: 618971，立即停止重复请求
2025-08-04 17:26:31 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 17:26:31 [信息] [线程1] 已清理响应文件
2025-08-04 17:26:31 线程1：[信息] [信息] 验证码获取成功: 618971，正在自动填入... (进度: 100%)
2025-08-04 17:26:31 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 17:26:31 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 17:26:31 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 17:26:31 [信息] 线程1完成第二页事件已处理
2025-08-04 17:26:31 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-04 17:26:31 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 17:26:33 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 17:26:33 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 17:26:33 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 17:26:34 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 17:26:34 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 17:26:35 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 17:26:35 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 17:26:35 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 17:26:36 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 17:26:39 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 17:26:39 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 17:26:39 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 17:26:43 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-04 17:26:43 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-04 17:26:47 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-04 17:26:47 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-04 17:26:51 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 17:26:51 [信息] 线程2获取已分配的榴莲手机号码: +526862110619
2025-08-04 17:26:51 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +526862110619 (进度: 38%)
2025-08-04 17:26:51 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 17:26:52 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 17:26:54 线程2：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 17:26:54 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 17:26:54 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 17:26:54 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 17:26:59 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 17:26:59 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 17:26:59 线程2：[信息] [信息] 已自动获取并填入手机号码: +526862110619 (进度: 38%)
2025-08-04 17:27:00 线程2：[信息] [信息] 使用已获取的手机号码: +526862110619（保存本地号码: +526862110619） (进度: 38%)
2025-08-04 17:27:00 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 17:27:03 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 17:27:04 线程2：[信息] [信息] 正在选择月份: January (进度: 38%)
2025-08-04 17:27:04 线程2：[信息] [信息] 已选择月份（标准选项）: January (进度: 38%)
2025-08-04 17:27:05 线程2：[信息] [信息] 正在选择年份: 2029 (进度: 38%)
2025-08-04 17:27:05 线程2：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 38%)
2025-08-04 17:27:05 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 17:27:05 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 17:27:05 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 17:27:08 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-04 17:27:08 [信息] 线程3获取已分配的榴莲手机号码: +527571510446
2025-08-04 17:27:08 线程3：[信息] [信息] 多线程模式：使用已分配的手机号码 +527571510446 (进度: 100%)
2025-08-04 17:27:09 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 17:27:09 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 17:27:11 线程3：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 17:27:11 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 17:27:11 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 17:27:11 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 17:27:12 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 17:27:12 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-04 17:27:12 [信息] 线程1获取已分配的榴莲手机号码: +523513061202
2025-08-04 17:27:12 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +523513061202 (进度: 100%)
2025-08-04 17:27:13 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 17:27:13 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 17:27:13 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 17:27:13 线程2：[信息] [信息] 已清空并重新填写手机号码: +526862110619 (进度: 38%)
2025-08-04 17:27:13 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-04 17:27:13 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 17:27:14 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-04 17:27:14 线程3：[信息] [信息] 已自动获取并填入手机号码: +527571510446 (进度: 100%)
2025-08-04 17:27:15 线程1：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 17:27:15 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 17:27:15 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 17:27:15 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 17:27:15 线程3：[信息] [信息] 使用已获取的手机号码: +527571510446（保存本地号码: +527571510446） (进度: 100%)
2025-08-04 17:27:15 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 17:27:15 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 17:27:16 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 17:27:16 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 17:27:16 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 17:27:16 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 17:27:17 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-04 17:27:18 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-04 17:27:18 线程1：[信息] [信息] 已自动获取并填入手机号码: +523513061202 (进度: 100%)
2025-08-04 17:27:18 线程3：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 17:27:19 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 17:27:19 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:27:19 线程3：[信息] [信息] 正在选择月份: May (进度: 100%)
2025-08-04 17:27:19 线程3：[信息] [信息] 已选择月份（标准选项）: May (进度: 100%)
2025-08-04 17:27:19 线程1：[信息] [信息] 使用已获取的手机号码: +523513061202（保存本地号码: +523513061202） (进度: 100%)
2025-08-04 17:27:19 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 17:27:20 线程3：[信息] [信息] 正在选择年份: 2027 (进度: 100%)
2025-08-04 17:27:20 线程3：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 100%)
2025-08-04 17:27:20 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 17:27:20 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 17:27:20 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 17:27:22 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 17:27:23 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35104 字节 (进度: 100%)
2025-08-04 17:27:23 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，35104字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:27:23 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:27:23 线程1：[信息] [信息] 正在选择月份: April (进度: 100%)
2025-08-04 17:27:23 线程1：[信息] [信息] 已选择月份（标准选项）: April (进度: 100%)
2025-08-04 17:27:24 线程1：[信息] [信息] 正在选择年份: 2028 (进度: 100%)
2025-08-04 17:27:24 线程1：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 100%)
2025-08-04 17:27:25 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 17:27:25 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 17:27:25 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 17:27:25 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 17:27:26 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"27zdd7"},"taskId":"3bc875c2-7115-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 17:27:26 线程2：[信息] [信息] 第六页第1次识别结果: 27zdd7 → 转换为小写: 27zdd7 (进度: 100%)
2025-08-04 17:27:26 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:27:26 线程2：[信息] [信息] 第六页已填入验证码: 27zdd7 (进度: 100%)
2025-08-04 17:27:26 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-04 17:27:26 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:27:26 线程3：[信息] [信息] 已清空并重新填写手机号码: +527571510446 (进度: 100%)
2025-08-04 17:27:26 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 17:27:28 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 17:27:28 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 17:27:28 线程3：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 17:27:28 线程3：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 17:27:28 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 17:27:29 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 17:27:29 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 17:27:30 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 17:27:31 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-04 17:27:31 线程1：[信息] [信息] 已清空并重新填写手机号码: +523513061202 (进度: 100%)
2025-08-04 17:27:31 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 17:27:31 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 17:27:31 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:27:32 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 17:27:33 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 17:27:33 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 17:27:33 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 17:27:33 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 17:27:33 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 17:27:35 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35169 字节 (进度: 100%)
2025-08-04 17:27:35 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，35169字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:27:35 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:27:35 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 17:27:35 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 17:27:35 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 17:27:35 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 17:27:36 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 17:27:36 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:27:37 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"954cgs"},"taskId":"4257c348-7115-11f0-948a-9ab6db23b9b7"} (进度: 100%)
2025-08-04 17:27:38 线程3：[信息] [信息] 第六页第1次识别结果: 954cgs → 转换为小写: 954cgs (进度: 100%)
2025-08-04 17:27:38 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:27:38 线程3：[信息] [信息] 第六页已填入验证码: 954cgs (进度: 100%)
2025-08-04 17:27:38 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:27:40 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 17:27:40 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 17:27:40 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 17:27:40 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 17:27:42 线程3：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 17:27:42 线程3：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-04 17:27:42 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34997 字节 (进度: 100%)
2025-08-04 17:27:42 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，34997字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:27:42 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:27:44 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"4x2r86"},"taskId":"46819c64-7115-11f0-ae6e-62c5329370b7"} (进度: 100%)
2025-08-04 17:27:44 线程1：[信息] [信息] 第六页第1次识别结果: 4x2r86 → 转换为小写: 4x2r86 (进度: 100%)
2025-08-04 17:27:44 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:27:44 线程1：[信息] [信息] 第六页已填入验证码: 4x2r86 (进度: 100%)
2025-08-04 17:27:44 线程3：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:27:44 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:27:45 线程2：[信息] [信息] 线程2验证码获取成功: 8691 (进度: 100%)
2025-08-04 17:27:45 [信息] 线程2手机号码已加入释放队列: +526862110619 (原因: 获取验证码成功)
2025-08-04 17:27:45 线程2：[信息] [信息] 线程2验证码获取成功: 8691，立即填入验证码... (进度: 100%)
2025-08-04 17:27:46 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 17:27:46 线程2：[信息] [信息] 线程2已自动填入手机验证码: 8691 (进度: 100%)
2025-08-04 17:27:48 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-04 17:27:48 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31673 字节 (进度: 100%)
2025-08-04 17:27:48 线程3：[信息] [信息] ✅ 图片验证通过：200x70px，31673字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:27:48 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 17:27:48 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:27:48 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 17:27:48 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 17:27:49 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"f752xb"},"taskId":"49f101d2-7115-11f0-9f1c-62c5329370b7"} (进度: 100%)
2025-08-04 17:27:49 线程3：[信息] [信息] 第六页第2次识别结果: f752xb → 转换为小写: f752xb (进度: 100%)
2025-08-04 17:27:49 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:27:49 线程3：[信息] [信息] 第六页已填入验证码: f752xb (进度: 100%)
2025-08-04 17:27:49 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:27:51 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 17:27:51 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 17:27:51 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 17:27:51 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 17:27:51 [信息] 开始释放1个手机号码
2025-08-04 17:27:51 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 17:27:51 [信息] [手机API] 释放手机号码: +526862110619
2025-08-04 17:27:51 [信息] [手机API] 手机号码释放成功: +526862110619
2025-08-04 17:27:52 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 17:27:52 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 17:27:52 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 17:27:53 线程3：[信息] [信息] 第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 17:27:53 线程3：[信息] [信息] 第六页第2次识别异常: 验证码错误 (进度: 100%)
2025-08-04 17:27:54 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 17:27:54 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 17:27:54 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 17:27:54 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 17:27:54 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 17:27:55 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 17:27:55 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 17:27:55 线程3：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:27:59 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31139 字节 (进度: 100%)
2025-08-04 17:27:59 线程3：[信息] [信息] ✅ 图片验证通过：200x70px，31139字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:27:59 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:27:59 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-04 17:27:59 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 17:27:59 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 17:27:59 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 17:27:59 线程1：[信息] [信息] 线程1验证码获取成功: 2927 (进度: 100%)
2025-08-04 17:27:59 [信息] 线程1手机号码已加入释放队列: +523513061202 (原因: 获取验证码成功)
2025-08-04 17:27:59 线程1：[信息] [信息] 线程1验证码获取成功: 2927，立即填入验证码... (进度: 100%)
2025-08-04 17:27:59 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 17:27:59 线程1：[信息] [信息] 线程1已自动填入手机验证码: 2927 (进度: 100%)
2025-08-04 17:28:00 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-04 17:28:00 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 17:28:01 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 17:28:01 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 17:28:01 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 17:28:01 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"znmcs5"},"taskId":"511c4070-7115-11f0-b791-029e46bc98e3"} (进度: 100%)
2025-08-04 17:28:01 线程3：[信息] [信息] 第六页第3次识别结果: znmcs5 → 转换为小写: znmcs5 (进度: 100%)
2025-08-04 17:28:01 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:28:01 线程3：[信息] [信息] 第六页已填入验证码: znmcs5 (进度: 100%)
2025-08-04 17:28:01 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:28:03 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 17:28:03 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 17:28:04 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 17:28:04 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 17:28:04 线程3：[信息] [信息] 第3次图形验证码识别成功 (进度: 100%)
2025-08-04 17:28:04 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 17:28:06 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 17:28:06 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 17:28:06 线程3：[信息] 已暂停
2025-08-04 17:28:06 [信息] 线程3已暂停
2025-08-04 17:28:06 [信息] 线程3已暂停
2025-08-04 17:28:06 线程3：[信息] [信息] 检测到注册已暂停或终止，停止图形验证码处理 (进度: 100%)
2025-08-04 17:28:07 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 17:28:07 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 17:28:08 线程3：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 6 (进度: 100%)
2025-08-04 17:28:08 线程3：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 17:28:08 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 17:28:08 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 17:28:08 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 17:28:08 线程3：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 17:28:08 线程3：[信息] [信息]  智能检测到当前在第7页 (进度: 100%)
2025-08-04 17:28:08 线程3：[信息] [信息] 智能检测到当前在第7页，开始智能处理... (进度: 100%)
2025-08-04 17:28:08 线程3：[信息] 已继续
2025-08-04 17:28:08 [信息] 线程3已继续
2025-08-04 17:28:15 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 17:28:15 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 17:28:15 线程3：[信息] 已暂停
2025-08-04 17:28:15 [信息] 线程3已暂停
2025-08-04 17:28:15 [信息] 线程3已暂停
2025-08-04 17:28:18 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 17:28:18 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 17:28:18 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 17:28:18 [信息] 成功点击更多按钮
2025-08-04 17:28:19 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 17:28:19 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 17:28:19 [信息] 成功点击账户信息按钮
2025-08-04 17:28:20 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 17:28:20 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 17:28:20 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 17:28:20 [信息] 成功定位到'安全凭证'链接
2025-08-04 17:28:21 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 17:28:21 [信息] 开始释放1个手机号码
2025-08-04 17:28:21 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 17:28:21 [信息] [手机API] 释放手机号码: +523513061202
2025-08-04 17:28:21 [信息] [手机API] 手机号码释放成功: +523513061202
2025-08-04 17:28:22 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 17:28:22 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 17:28:28 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 17:28:28 [信息] 成功点击'安全凭证'链接
2025-08-04 17:28:28 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 17:28:38 线程1：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" })
  -   locator resolved to <a data-testid="management-console-button" href="ht…>…</a>
  - attempting click action
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   performing click action (进度: 100%)
2025-08-04 17:28:38 [信息] 控制台按钮处理失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" })
  -   locator resolved to <a data-testid="management-console-button" href="ht…>…</a>
  - attempting click action
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   performing click action
2025-08-04 17:28:38 线程1：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 17:28:38 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 17:28:38 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：op7x25cEKWI ③AWS密码：Tee2uke4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 17:28:38 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：op7x25cEKWI ③AWS密码：Tee2uke4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:28:38 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：op7x25cEKWI ③AWS密码：Tee2uke4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:28:38 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：op7x25cEKWI ③AWS密码：Tee2uke4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:28:38 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：op7x25cEKWI ③AWS密码：Tee2uke4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:28:38 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：op7x25cEKWI ③AWS密码：Tee2uke4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:28:38 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 17:28:38 线程1：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 17:28:39 线程1：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 17:28:39 [信息] 注册完成（密钥提取失败）
2025-08-04 17:28:39 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 17:28:39 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 17:28:39 [信息] 已完成数据移除: <EMAIL>
2025-08-04 17:28:39 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 17:28:40 线程1：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 17:28:40 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 17:28:45 线程3：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 7 (进度: 100%)
2025-08-04 17:28:45 线程3：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 17:28:45 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 17:28:45 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 17:28:45 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 17:28:45 线程3：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 17:28:45 线程3：[信息] [信息]  智能检测到当前在第7页 (进度: 100%)
2025-08-04 17:28:45 线程3：[信息] [信息] 智能检测到当前在第7页，开始智能处理... (进度: 100%)
2025-08-04 17:28:45 线程3：[信息] 已继续
2025-08-04 17:28:45 [信息] 线程3已继续
2025-08-04 17:28:47 线程2：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 17:28:47 线程2：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 17:28:47 线程2：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 17:28:48 线程2：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 17:28:48 [信息] 页面缩放设置为50%完成
2025-08-04 17:28:48 线程2：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 17:28:48 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 17:28:48 线程2：[信息] [信息] ✅ 找到下一步按钮，使用选择器: Locator(button[data-testid*='confirm']) (进度: 100%)
2025-08-04 17:28:48 [信息] 找到下一步按钮，使用选择器: Locator(button[data-testid*='confirm'])
2025-08-04 17:29:13 [信息] 获取线程3当前数据: <EMAIL>
2025-08-04 17:29:13 线程3：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 17:29:13 线程3：[信息] 数据详情: <EMAIL>|72vQ1y1Z|Ramirez Nata|Cencosud|Freire 420, curico, chile|Maule|Curico|3340000|5176085103000772|05|27|316|Ramirez Nata|u6G89fe2M7|CL
2025-08-04 17:29:13 线程3：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 17:29:13 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 17:29:13 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:29:13 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:29:13 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:29:13 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:29:13 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:29:13 线程3：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 17:29:13 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 17:29:13 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 17:29:13 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:29:13 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:29:13 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:29:13 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:29:13 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：u6G89fe2M7 ③AWS密码：72vQ1y1Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:29:13 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 17:29:14 线程3：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_3_20250804_171051
2025-08-04 17:29:14 线程3：[信息] 已终止
2025-08-04 17:29:14 [信息] 线程3已终止
2025-08-04 17:29:14 [信息] 开始处理线程3终止数据，共1个数据
2025-08-04 17:29:14 [信息] 处理线程3终止数据: <EMAIL>
2025-08-04 17:29:14 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 17:29:14 [信息] 线程3终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 17:29:14 [信息] 线程3终止数据处理完成，成功移动1个数据
2025-08-04 17:29:14 [信息] UniformGrid列数已更新为: 1
2025-08-04 17:29:14 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 17:29:14 [信息] 线程3已终止
2025-08-04 17:29:18 [信息] 点击'下一步'按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for Locator("button[data-testid*='confirm']").First
  -   locator resolved to <button type="submit" data-analytics-type="eventDetail" …>…</button>
  - attempting click action
  -   waiting for element to be visible, enabled and stable
  -     element is not visible - waiting...
2025-08-04 17:29:18 线程2：[信息] [信息] ⚠️ 点击'下一步'按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for Locator("button[data-testid*='confirm']").First
  -   locator resolved to <button type="submit" data-analytics-type="eventDetail" …>…</button>
  - attempting click action
  -   waiting for element to be visible, enabled and stable
  -     element is not visible - waiting... (进度: 100%)
2025-08-04 17:29:18 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 17:29:18 [信息] 开始创建和复制访问密钥
2025-08-04 17:29:18 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 17:29:18 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 17:29:18 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 17:29:18 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 17:29:18 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 17:29:18 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 17:29:20 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 17:29:20 线程2：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 17:29:20 [信息] 使用id属性定位到确认复选框
2025-08-04 17:29:20 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 17:29:20 [信息] 成功勾选确认复选框
2025-08-04 17:29:21 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 17:29:22 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 17:29:22 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 17:29:25 线程2：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 17:29:25 [信息] 开始复制访问密钥
2025-08-04 17:29:27 线程2：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 17:29:27 [信息] 方法2找到 2 个单元格
2025-08-04 17:29:27 线程2：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 17:29:27 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 17:29:27 [信息] 单元格[0]: 'AKIAS3SG5TAFRTP5MZXY'
2025-08-04 17:29:27 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 17:29:27 线程2：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 17:29:27 线程2：[信息] [信息] ✅ 找到访问密钥: AKIAS3SG5TAFRTP5MZXY (进度: 100%)
2025-08-04 17:29:27 [信息] 找到访问密钥: AKIAS3SG5TAFRTP5MZXY
2025-08-04 17:29:41 线程2：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 17:29:41 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 17:29:42 线程2：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 17:29:42 线程2：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 17:29:42 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 17:29:42 线程2：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 17:29:42 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 17:29:42 线程2：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 17:29:42 [信息] 使用TestId定位到显示按钮
2025-08-04 17:29:43 线程2：[信息] [信息] ✅ 显示按钮点击成功，新文本: nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar (进度: 100%)
2025-08-04 17:29:43 [信息] 显示按钮点击成功，新文本: nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar
2025-08-04 17:29:43 线程2：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar (进度: 100%)
2025-08-04 17:29:43 [信息] 直接从显示文本提取秘密访问密钥: nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar
2025-08-04 17:29:43 线程2：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 17:29:43 [信息] 访问密钥复制完成 - AccessKey: AKIAS3SG5TAFRTP5MZXY, SecretKey: nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar
2025-08-04 17:29:44 线程2：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 17:29:45 线程2：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 17:29:45 [信息] 成功点击'已完成'按钮
2025-08-04 17:29:45 线程2：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIAS3SG5TAFRTP5MZXY, SecretKey: nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar (进度: 100%)
2025-08-04 17:29:45 [信息] 密钥已保存到数据对象 - AccessKey: AKIAS3SG5TAFRTP5MZXY, SecretKey: nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar
2025-08-04 17:29:46 线程2：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 17:29:49 线程2：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 17:29:49 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 17:29:49 线程2：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 17:29:49 [信息] 开始设置MFA设备
2025-08-04 17:29:49 线程2：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 17:29:49 线程2：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 17:29:49 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 17:29:49 线程2：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 17:30:09 线程2：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 100%)
2025-08-04 17:30:09 线程2：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 100%)
2025-08-04 17:30:09 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 17:30:09 线程2：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-04 17:30:09 线程2：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-04 17:30:09 线程2：[信息] 已继续
2025-08-04 17:30:09 [信息] 线程2已继续
2025-08-04 17:30:52 [信息] 获取线程2当前数据: <EMAIL>
2025-08-04 17:30:52 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 17:30:52 线程2：[信息] 数据详情: <EMAIL>|q82x8CxA|Ramirez Nata|Falabella|Freire 420, curico, chile|Maule|Curico|3340000|4757749015929192|01|29|946|Ramirez Nata|OgGM0Yk38I5|CL
2025-08-04 17:30:52 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 17:30:52 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 17:30:52 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:30:52 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:30:52 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:30:52 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:30:52 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:30:52 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:30:52 [信息] 多线程状态已重置
2025-08-04 17:30:52 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:30:52 [信息] 多线程状态已重置
2025-08-04 17:30:52 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 17:30:52 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:30:52 [信息] 多线程状态已重置
2025-08-04 17:30:52 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 17:30:52 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:30:52 [信息] 多线程状态已重置
2025-08-04 17:30:52 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥：AKIAS3SG5TAFRTP5MZXY ⑤秘密访问密钥：nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 17:30:52 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥：AKIAS3SG5TAFRTP5MZXY ⑤秘密访问密钥：nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar ⑥MFA信息：   //手动终止
2025-08-04 17:30:52 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥：AKIAS3SG5TAFRTP5MZXY ⑤秘密访问密钥：nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar ⑥MFA信息：   //手动终止
2025-08-04 17:30:52 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥：AKIAS3SG5TAFRTP5MZXY ⑤秘密访问密钥：nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar ⑥MFA信息：   //手动终止
2025-08-04 17:30:52 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥：AKIAS3SG5TAFRTP5MZXY ⑤秘密访问密钥：nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar ⑥MFA信息：   //手动终止
2025-08-04 17:30:52 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：OgGM0Yk38I5 ③AWS密码：q82x8CxA ④访问密钥：AKIAS3SG5TAFRTP5MZXY ⑤秘密访问密钥：nMQ5doHDmEVfQBCnq86Y4BIpr9tC0ACiFLJ0ZXrfOcultar ⑥MFA信息：   //手动终止
2025-08-04 17:30:52 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:30:52 [信息] 多线程状态已重置
2025-08-04 17:30:52 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 17:30:52 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250804_171051
2025-08-04 17:30:52 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:30:52 [信息] 多线程状态已重置
2025-08-04 17:30:52 线程2：[信息] 已终止
2025-08-04 17:30:52 [信息] 线程2已终止
2025-08-04 17:30:52 [信息] 开始处理线程2终止数据，共1个数据
2025-08-04 17:30:52 [信息] 处理线程2终止数据: <EMAIL>
2025-08-04 17:30:52 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 17:30:52 [信息] 线程2终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 17:30:52 [信息] 线程2终止数据处理完成，成功移动1个数据
2025-08-04 17:30:52 [信息] UniformGrid列数已更新为: 1
2025-08-04 17:30:52 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 17:30:52 [信息] 线程2已终止
2025-08-04 17:32:04 [信息] 多线程窗口引用已清理
2025-08-04 17:32:04 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 17:32:04 [信息] 多线程管理窗口正在关闭
2025-08-04 17:32:06 [信息] 程序正在退出，开始清理工作...
2025-08-04 17:32:06 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 17:32:06 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 17:32:06 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 17:32:06 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 17:32:06 [信息] 程序退出清理工作完成
