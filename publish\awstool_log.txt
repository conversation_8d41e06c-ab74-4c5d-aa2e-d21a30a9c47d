2025-08-04 16:56:32 [信息] 线程数量已选择: 3
2025-08-04 16:56:37 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 16:56:37 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 16:56:37 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 20
2025-08-04 16:56:37 [信息] 所有线程已停止并清理
2025-08-04 16:56:37 [信息] 正在初始化多线程服务...
2025-08-04 16:56:37 [信息] 榴莲手机API服务已初始化
2025-08-04 16:56:37 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-04 16:56:37 [信息] 多线程服务初始化完成
2025-08-04 16:56:37 [信息] 数据分配完成：共20条数据分配给3个线程
2025-08-04 16:56:37 [信息] 线程1分配到7条数据
2025-08-04 16:56:37 [信息] 线程2分配到7条数据
2025-08-04 16:56:37 [信息] 线程3分配到6条数据
2025-08-04 16:56:37 [信息] 屏幕工作区域: 1280x672
2025-08-04 16:56:37 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 16:56:37 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 16:56:37 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 16:56:37 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_009, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=6 GB
2025-08-04 16:56:37 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 16:56:37 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 16:56:37 [信息] 屏幕工作区域: 1280x672
2025-08-04 16:56:37 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 16:56:37 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 16:56:37 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 16:56:37 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=10核, RAM=20 GB
2025-08-04 16:56:37 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 16:56:37 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 16:56:37 [信息] 屏幕工作区域: 1280x672
2025-08-04 16:56:37 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 16:56:37 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 16:56:37 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 16:56:37 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=4 GB
2025-08-04 16:56:37 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 16:56:37 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 16:56:37 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 16:56:37 [信息] 多线程注册启动成功，共3个线程
2025-08-04 16:56:37 线程1：[信息] 开始启动注册流程
2025-08-04 16:56:37 线程2：[信息] 开始启动注册流程
2025-08-04 16:56:37 线程3：[信息] 开始启动注册流程
2025-08-04 16:56:37 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 16:56:37 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-04 16:56:37 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 16:56:37 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 16:56:37 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 16:56:37 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 16:56:37 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 16:56:37 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 16:56:37 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 16:56:37 [信息] 多线程管理窗口已初始化
2025-08-04 16:56:37 [信息] UniformGrid列数已更新为: 1
2025-08-04 16:56:37 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 16:56:37 [信息] 多线程管理窗口已打开
2025-08-04 16:56:37 [信息] 多线程注册启动成功，共3个线程
2025-08-04 16:56:40 [信息] UniformGrid列数已更新为: 1
2025-08-04 16:56:40 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 16:56:40 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 16:56:40 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 16:56:40 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-04 16:56:40 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 16:56:41 [信息] UniformGrid列数已更新为: 1
2025-08-04 16:56:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 16:56:41 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 16:56:41 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 16:56:41 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 16:56:41 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 16:56:41 [信息] UniformGrid列数已更新为: 2
2025-08-04 16:56:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 16:56:41 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 16:56:41 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 16:56:41 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-08-04 16:56:41 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 16:56:42 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 16:56:43 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 16:56:43 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 16:56:44 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 16:56:44 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 16:56:45 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 16:56:45 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 16:56:45 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 16:56:45 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 16:56:45 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 16:56:45 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 10核 (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=10核, RAM=20 GB
2025-08-04 16:56:45 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 18核 (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=4 GB
2025-08-04 16:56:45 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 16:56:45 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 16:56:45 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-04 16:56:45 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_009, CPU: 12核 (进度: 0%)
2025-08-04 16:56:45 [信息] 浏览器指纹注入: Canvas=canvas_fp_009, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=6 GB
2025-08-04 16:56:46 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 16:56:46 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 16:56:47 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 16:56:48 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 10
   • 设备内存: 20 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3A4B5C6D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-O1P2Q3R
   • MAC地址: 9A-BC-DE-F0-12-34
   • 屏幕分辨率: 1743x1179
   • 可用区域: 1743x1139

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A1B2C3D4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wimax
   • 电池API支持: True
   • 电池电量: 0.93
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 16:56:48 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 10    • 设备内存: 20 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3A4B5C6D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-O1P2Q3R    • MAC地址: 9A-BC-DE-F0-12-34    • 屏幕分辨率: 1743x1179    • 可用区域: 1743x1139   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A1B2C3D4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wimax    • 电池API支持: True    • 电池电量: 0.93    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 16:56:48 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 12
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: enabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3A4B5C6D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_001
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-K8L9M0N
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 2046x1037
   • 可用区域: 2046x997

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A1B2C3D4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: bluetooth
   • 电池API支持: True
   • 电池电量: 0.68
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 16:56:48 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 12    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: enabled   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3A4B5C6D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_001    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-K8L9M0N    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 2046x1037    • 可用区域: 2046x997   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A1B2C3D4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: bluetooth    • 电池API支持: True    • 电池电量: 0.68    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 16:56:48 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 18
   • 设备内存: 4 GB
   • 平台信息: Win32
   • Do Not Track: disabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_003
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: 12-34-56-78-9A-BC
   • 屏幕分辨率: 1832x1136
   • 可用区域: 1832x1096

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E5F6A7B8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: ethernet
   • 电池API支持: True
   • 电池电量: 0.44
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 16:56:48 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 18    • 设备内存: 4 GB    • 平台信息: Win32    • Do Not Track: disabled   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_003    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: 12-34-56-78-9A-BC    • 屏幕分辨率: 1832x1136    • 可用区域: 1832x1096   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E5F6A7B8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: ethernet    • 电池API支持: True    • 电池电量: 0.44    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 16:56:48 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 16:56:48 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 16:56:48 线程2：[信息] 浏览器启动成功
2025-08-04 16:56:48 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 16:56:49 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 16:56:49 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 16:56:49 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 16:56:49 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 16:56:49 线程3：[信息] 浏览器启动成功
2025-08-04 16:56:49 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 16:56:49 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 16:56:49 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 16:56:49 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 16:56:49 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 16:56:50 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 16:56:50 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 16:56:50 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 16:56:50 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 16:56:50 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 16:56:50 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 16:56:50 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 16:56:50 线程1：[信息] 浏览器启动成功
2025-08-04 16:56:50 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 16:56:50 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 16:56:50 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 16:56:51 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 16:56:51 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 16:56:51 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 16:56:51 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 16:56:51 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 16:56:51 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-04 16:56:51 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 16:56:51 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 16:56:51 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 16:56:51 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 16:56:51 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 16:56:51 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 16:56:51 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 16:57:19 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-04 16:57:19 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 16:57:19 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 16:57:19 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 16:57:19 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 16:57:19 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 16:57:19 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-04 16:57:19 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 16:57:19 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 16:57:20 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 16:57:20 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 16:57:20 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 16:57:21 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 16:57:21 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 16:57:21 [信息] 第一页相关失败，数据保持不动
2025-08-04 16:57:21 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 16:57:21 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 16:57:22 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 16:57:22 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 16:57:22 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 16:57:22 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 16:57:23 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 16:57:24 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 16:57:24 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 16:57:24 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 16:57:24 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 16:57:24 线程3：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-04 16:57:24 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 16:57:24 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 16:57:24 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 16:57:24 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 16:57:26 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 16:57:26 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 16:57:28 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 16:57:28 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 16:57:28 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 16:57:28 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 16:57:28 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 16:57:28 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 16:57:28 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 16:57:32 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35189 字节 (进度: 100%)
2025-08-04 16:57:32 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35189字节，复杂度符合要求 (进度: 100%)
2025-08-04 16:57:32 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 16:57:34 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"g227b7"},"taskId":"103dd766-7111-11f0-948a-9ab6db23b9b7"} (进度: 100%)
2025-08-04 16:57:34 线程3：[信息] [信息] 第一页第1次识别结果: g227b7 → 转换为小写: g227b7 (进度: 100%)
2025-08-04 16:57:34 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 16:57:34 线程3：[信息] [信息] 已填入验证码: g227b7 (进度: 100%)
2025-08-04 16:57:35 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 16:57:36 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35857 字节 (进度: 100%)
2025-08-04 16:57:36 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35857字节，复杂度符合要求 (进度: 100%)
2025-08-04 16:57:36 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 16:57:37 线程3：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 16:57:37 线程3：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 16:57:37 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 16:57:37 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 16:57:37 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 16:57:37 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 16:57:37 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 16:57:37 线程3：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 16:57:37 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 16:57:37 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 16:57:37 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 16:57:37 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"wxrfgx"},"taskId":"11e87f44-7111-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 16:57:37 线程2：[信息] [信息] 第一页第1次识别结果: wxrfgx → 转换为小写: wxrfgx (进度: 100%)
2025-08-04 16:57:37 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 16:57:37 线程2：[信息] [信息] 已填入验证码: wxrfgx (进度: 100%)
2025-08-04 16:57:37 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 16:57:39 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 16:57:39 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 16:57:39
2025-08-04 16:57:39 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 16:57:39 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 16:57:39 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 16:57:40 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 16:57:40 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 16:57:40 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 16:57:40 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 16:57:40 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 16:57:40 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 16:57:40 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 16:57:40 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 16:57:42 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 16:57:42 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 16:57:42
2025-08-04 16:57:42 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 16:57:42 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 16:57:42
2025-08-04 16:57:45 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 16:57:45 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 16:57:45
2025-08-04 16:57:45 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 16:57:45 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 16:57:45
2025-08-04 16:57:48 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 16:57:48 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 16:57:48
2025-08-04 16:57:48 [信息] [线程3] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 16:57:48 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 16:57:48
2025-08-04 16:57:49 [信息] [线程3] 邮箱验证码获取成功: 564444，立即停止重复请求
2025-08-04 16:57:49 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 16:57:49 [信息] [线程3] 已清理响应文件
2025-08-04 16:57:49 线程3：[信息] [信息] 验证码获取成功: 564444，正在自动填入... (进度: 25%)
2025-08-04 16:57:49 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 16:57:49 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 16:57:49 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 16:57:49 [信息] 线程3完成第二页事件已处理
2025-08-04 16:57:49 [信息] 线程3完成第二页，开始批量获取手机号码...
2025-08-04 16:57:49 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 16:57:49 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-04 16:57:49 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-04 16:57:51 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 16:57:51 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 16:57:51
2025-08-04 16:57:52 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 16:57:53 线程3：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 16:57:53 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 16:57:53 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 16:57:53 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 16:57:53 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+524762441787","+523411778684","+526251306723"]}
2025-08-04 16:57:53 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-04 16:57:53 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-04 16:57:53 [信息] 线程1分配榴莲手机号码: +524762441787
2025-08-04 16:57:53 [信息] 线程2分配榴莲手机号码: +523411778684
2025-08-04 16:57:53 [信息] 线程3分配榴莲手机号码: +526251306723
2025-08-04 16:57:53 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-04 16:57:53 [信息] 批量获取3个手机号码成功
2025-08-04 16:57:54 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 16:57:54 [信息] [线程2] 第5次触发邮箱验证码获取...（最多20次）
2025-08-04 16:57:54 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 16:57:54
2025-08-04 16:57:57 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 16:57:57 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 16:57:57 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 16:57:57 [信息] [线程2] 第6次触发邮箱验证码获取...（最多20次）
2025-08-04 16:57:57 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 16:57:57
2025-08-04 16:58:01 [信息] [线程2] 第7次触发邮箱验证码获取...（最多20次）
2025-08-04 16:58:01 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 16:58:01
2025-08-04 16:58:01 [信息] [线程2] 邮箱验证码获取成功: 050088，立即停止重复请求
2025-08-04 16:58:03 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 16:58:03 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 16:58:09 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 16:58:09 [信息] [线程2] 已清理响应文件
2025-08-04 16:58:09 线程2：[信息] [信息] 验证码获取成功: 050088，正在自动填入... (进度: 25%)
2025-08-04 16:58:09 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 16:58:09 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 16:58:09 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 16:58:09 [信息] 线程2完成第二页事件已处理
2025-08-04 16:58:09 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-04 16:58:09 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 16:58:13 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 16:58:13 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 16:58:13 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 16:58:13 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 16:58:13 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 16:58:14 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 16:58:17 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 16:58:17 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 16:58:17 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 16:58:27 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 16:58:27 [信息] 线程3获取已分配的榴莲手机号码: +526251306723
2025-08-04 16:58:27 线程3：[信息] [信息] 多线程模式：使用已分配的手机号码 +526251306723 (进度: 38%)
2025-08-04 16:58:27 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 16:58:27 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 16:58:28 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 16:58:28 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 16:58:29 线程3：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 16:58:29 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 16:58:29 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 16:58:29 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 16:58:32 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 16:58:33 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 16:58:33 线程3：[信息] [信息] 已自动获取并填入手机号码: +526251306723 (进度: 38%)
2025-08-04 16:58:34 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 16:58:34 [信息] 线程2获取已分配的榴莲手机号码: +523411778684
2025-08-04 16:58:34 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +523411778684 (进度: 38%)
2025-08-04 16:58:34 线程3：[信息] [信息] 使用已获取的手机号码: +526251306723（保存本地号码: +526251306723） (进度: 38%)
2025-08-04 16:58:34 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 16:58:35 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 16:58:35 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 16:58:38 线程2：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 16:58:38 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 16:58:38 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 16:58:39 线程3：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 16:58:39 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 16:58:40 线程3：[信息] [信息] 正在选择月份: September (进度: 38%)
2025-08-04 16:58:40 线程3：[信息] [信息] 已选择月份（标准选项）: September (进度: 38%)
2025-08-04 16:58:41 线程3：[信息] [信息] 正在选择年份: 2026 (进度: 38%)
2025-08-04 16:58:41 线程3：[信息] [信息] 已选择年份（标准选项）: 2026 (进度: 38%)
2025-08-04 16:58:41 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 16:58:41 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 16:58:41 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 16:58:42 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 16:58:44 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 16:58:44 线程2：[信息] [信息] 已自动获取并填入手机号码: +523411778684 (进度: 38%)
2025-08-04 16:58:45 线程2：[信息] [信息] 使用已获取的手机号码: +523411778684（保存本地号码: +523411778684） (进度: 38%)
2025-08-04 16:58:45 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 16:58:48 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 16:58:49 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 16:58:49 线程3：[信息] [信息] 已清空并重新填写手机号码: +526251306723 (进度: 38%)
2025-08-04 16:58:49 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 16:58:51 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 16:58:53 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 16:58:53 线程3：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 16:58:53 线程3：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 16:58:53 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 16:58:54 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] ❌ 按钮匹配失败: Target page, context or browser has been closed (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] 🔬 执行详细页面分析... (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] ❌ 详细分析失败: Target page, context or browser has been closed (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] 检测当前页面状态... (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] ❌ 按钮匹配失败: Target page, context or browser has been closed (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] 🔬 执行详细页面分析... (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] ❌ 详细分析失败: Target page, context or browser has been closed (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] [信息] 无法检测到当前页面，请手动操作 (进度: 0%)
2025-08-04 16:58:54 线程1：[信息] 已继续
2025-08-04 16:58:54 [信息] 线程1已继续
2025-08-04 16:58:55 线程1：[信息] [信息] 所有自动线程已停止 (进度: 0%)
2025-08-04 16:58:55 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 0%)
2025-08-04 16:58:55 线程1：[信息] 已暂停
2025-08-04 16:58:55 [信息] 线程1已暂停
2025-08-04 16:58:55 [信息] 线程1已暂停
2025-08-04 16:58:56 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 16:58:56 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 16:58:58 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 16:58:59 线程2：[信息] [信息] 正在选择月份: June (进度: 38%)
2025-08-04 16:59:00 线程2：[信息] [信息] 已选择月份（标准选项）: June (进度: 38%)
2025-08-04 16:59:00 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-04 16:59:01 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-04 16:59:01 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 16:59:01 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 16:59:01 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 16:59:04 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34800 字节 (进度: 100%)
2025-08-04 16:59:04 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34800字节，复杂度符合要求 (进度: 100%)
2025-08-04 16:59:04 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 16:59:06 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"zbh866"},"taskId":"46b1a6f6-7111-11f0-9768-ba03bdd70631"} (进度: 100%)
2025-08-04 16:59:06 线程3：[信息] [信息] 第六页第1次识别结果: zbh866 → 转换为小写: zbh866 (进度: 100%)
2025-08-04 16:59:06 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 16:59:06 线程3：[信息] [信息] 第六页已填入验证码: zbh866 (进度: 100%)
2025-08-04 16:59:06 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 16:59:09 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 16:59:09 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 16:59:09 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 16:59:10 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 16:59:10 线程2：[信息] [信息] 已清空并重新填写手机号码: +523411778684 (进度: 38%)
2025-08-04 16:59:11 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 16:59:12 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 16:59:13 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 16:59:13 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 16:59:13 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 16:59:13 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 16:59:13 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 16:59:15 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 16:59:15 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 16:59:15 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 16:59:15 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 16:59:16 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 16:59:16 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 16:59:20 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-04 16:59:20 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 16:59:20 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 16:59:20 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 16:59:20 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34839 字节 (进度: 100%)
2025-08-04 16:59:20 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，34839字节，复杂度符合要求 (进度: 100%)
2025-08-04 16:59:20 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 16:59:22 线程3：[信息] [信息] 线程3验证码获取成功: 4144 (进度: 100%)
2025-08-04 16:59:22 [信息] 线程3手机号码已加入释放队列: +526251306723 (原因: 获取验证码成功)
2025-08-04 16:59:23 线程3：[信息] [信息] 线程3验证码获取成功: 4144，立即填入验证码... (进度: 100%)
2025-08-04 16:59:23 线程3：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 16:59:23 线程3：[信息] [信息] 线程3已自动填入手机验证码: 4144 (进度: 100%)
2025-08-04 16:59:24 线程3：[信息] [信息] 线程3正在自动点击Continue按钮... (进度: 100%)
2025-08-04 16:59:24 线程3：[信息] [信息] 线程3手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 16:59:25 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"76hbtm"},"taskId":"522ccc04-7111-11f0-b791-029e46bc98e3"} (进度: 100%)
2025-08-04 16:59:25 线程2：[信息] [信息] 第六页第1次识别结果: 76hbtm → 转换为小写: 76hbtm (进度: 100%)
2025-08-04 16:59:25 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 16:59:25 线程2：[信息] [信息] 第六页已填入验证码: 76hbtm (进度: 100%)
2025-08-04 16:59:25 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 16:59:28 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 16:59:29 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 16:59:29 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 16:59:32 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 16:59:35 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 16:59:35 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 16:59:35 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 16:59:35 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 16:59:36 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 16:59:36 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 16:59:36 线程3：[信息] 已暂停
2025-08-04 16:59:36 [信息] 线程3已暂停
2025-08-04 16:59:36 [信息] 线程3已暂停
2025-08-04 16:59:37 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 16:59:37 [信息] 开始释放1个手机号码
2025-08-04 16:59:37 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 16:59:37 [信息] [手机API] 释放手机号码: +526251306723
2025-08-04 16:59:37 线程3：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 16:59:38 [信息] [手机API] 手机号码释放成功: +526251306723
2025-08-04 16:59:38 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 16:59:38 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 16:59:40 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 16:59:40 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 16:59:40 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 16:59:40 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 16:59:40 线程2：[信息] [信息] 线程2验证码获取成功: 2341 (进度: 100%)
2025-08-04 16:59:40 [信息] 线程2手机号码已加入释放队列: +523411778684 (原因: 获取验证码成功)
2025-08-04 16:59:40 线程2：[信息] [信息] 线程2验证码获取成功: 2341，立即填入验证码... (进度: 100%)
2025-08-04 16:59:40 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 16:59:40 线程2：[信息] [信息] 线程2已自动填入手机验证码: 2341 (进度: 100%)
2025-08-04 16:59:41 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-04 16:59:41 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 16:59:44 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 16:59:45 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 16:59:46 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 16:59:47 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 16:59:49 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 16:59:49 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 16:59:57 [信息] 获取线程3当前数据: <EMAIL>
2025-08-04 16:59:57 线程3：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 16:59:57 线程3：[信息] 数据详情: <EMAIL>|41qTP0xW|Majluf Naife|Colbun|Av la dehesa 224, dpto 518|Region Metropolitana|Santiago|7690000|5331870014520154|09|26|478|Majluf Naife|6RgY1V24L|CL
2025-08-04 16:59:57 线程3：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 16:59:58 线程3：[信息] [信息] 点击完成注册按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-08-04 16:59:58 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 16:59:58 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 16:59:58 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 16:59:58 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 16:59:58 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 16:59:58 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 16:59:58 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 16:59:58 线程3：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 16:59:58 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 16:59:58 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 16:59:58 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 16:59:58 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 16:59:58 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 16:59:58 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 16:59:58 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：6RgY1V24L ③AWS密码：41qTP0xW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 16:59:58 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 16:59:58 线程3：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_3_20250804_165637
2025-08-04 16:59:58 线程3：[信息] 已终止
2025-08-04 16:59:58 [信息] 线程3已终止
2025-08-04 16:59:58 [信息] 开始处理线程3终止数据，共1个数据
2025-08-04 16:59:58 [信息] 处理线程3终止数据: <EMAIL>
2025-08-04 16:59:58 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 16:59:58 [信息] 线程3终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 16:59:58 [信息] 线程3终止数据处理完成，成功移动1个数据
2025-08-04 16:59:58 [信息] UniformGrid列数已更新为: 1
2025-08-04 16:59:58 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 16:59:58 [信息] 线程3已终止
2025-08-04 17:00:00 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 17:00:00 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 17:00:00 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 17:00:01 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 88%)
2025-08-04 17:00:01 线程3：[信息] [信息] 注册已被终止，停止密钥提取流程 (进度: 98%)
2025-08-04 17:00:01 线程3：[信息] [信息] 注册已被终止 (进度: 98%)
2025-08-04 17:00:07 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 17:00:07 [信息] 开始释放1个手机号码
2025-08-04 17:00:07 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 17:00:07 [信息] [手机API] 释放手机号码: +523411778684
2025-08-04 17:00:07 [信息] [手机API] 手机号码释放成功: +523411778684
2025-08-04 17:00:08 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 17:00:08 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 17:00:19 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 17:00:19 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 17:00:19 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 17:00:19 [信息] 成功点击更多按钮
2025-08-04 17:00:20 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 17:00:20 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 17:00:20 [信息] 成功点击账户信息按钮
2025-08-04 17:00:21 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 17:00:21 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 17:00:21 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 17:00:21 [信息] 成功定位到'安全凭证'链接
2025-08-04 17:00:29 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 17:00:29 [信息] 成功点击'安全凭证'链接
2025-08-04 17:00:29 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 17:00:49 线程2：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 17:00:49 线程2：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 17:00:49 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 17:00:49 [信息] 开始创建和复制访问密钥
2025-08-04 17:00:49 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 17:00:49 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 17:00:49 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 17:00:49 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 17:01:00 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 17:01:00 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 17:01:02 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 17:01:02 线程2：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 17:01:02 [信息] 使用id属性定位到确认复选框
2025-08-04 17:01:02 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 17:01:02 [信息] 成功勾选确认复选框
2025-08-04 17:01:03 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 17:01:14 线程2：[信息] [信息] ❌ 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for Locator("button[type='button']").Last to be visible
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button> (进度: 100%)
2025-08-04 17:01:14 [信息] 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for Locator("button[type='button']").Last to be visible
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
  -   locator resolved to hidden <button type="button" data-awsui-analytics-label="" titl…>…</button>
2025-08-04 17:01:14 线程2：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 17:01:14 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 17:01:15 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：14k5LIaFD ③AWS密码：SM5n7a0v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 17:01:15 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：14k5LIaFD ③AWS密码：SM5n7a0v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:01:15 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：14k5LIaFD ③AWS密码：SM5n7a0v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:01:15 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：14k5LIaFD ③AWS密码：SM5n7a0v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:01:15 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：14k5LIaFD ③AWS密码：SM5n7a0v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:01:15 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：14k5LIaFD ③AWS密码：SM5n7a0v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:01:15 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 17:01:15 线程2：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 17:01:15 线程2：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 17:01:15 [信息] 注册完成（密钥提取失败）
2025-08-04 17:01:15 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 17:01:15 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 17:01:15 [信息] 已完成数据移除: <EMAIL>
2025-08-04 17:01:15 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 17:01:15 线程2：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 17:01:15 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 17:01:55 [信息] 多线程窗口引用已清理
2025-08-04 17:01:55 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 17:01:55 [信息] 多线程管理窗口正在关闭
2025-08-04 17:01:56 [信息] 程序正在退出，开始清理工作...
2025-08-04 17:01:56 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 17:01:56 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 17:01:56 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 17:01:56 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 17:01:56 [信息] 程序退出清理工作完成
