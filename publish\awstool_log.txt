2025-08-04 17:49:54 [信息] AWS自动注册工具启动
2025-08-04 17:49:54 [信息] 程序版本: 1.0.0.0
2025-08-04 17:49:54 [信息] 启动时间: 2025-08-04 17:49:54
2025-08-04 17:49:54 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 17:49:54 [信息] 线程数量已选择: 1
2025-08-04 17:49:54 [信息] 线程数量选择初始化完成
2025-08-04 17:49:54 [信息] 程序初始化完成
2025-08-04 17:50:02 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 17:50:04 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 17:50:05 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 17:50:05 [信息] 成功加载 15 条数据
2025-08-04 17:50:07 [信息] 线程数量已选择: 3
2025-08-04 17:50:10 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 17:50:10 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 17:50:10 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 15
2025-08-04 17:50:10 [信息] 所有线程已停止并清理
2025-08-04 17:50:10 [信息] 正在初始化多线程服务...
2025-08-04 17:50:10 [信息] 千川手机API服务已初始化
2025-08-04 17:50:10 [信息] 手机号码管理器已初始化，服务商: Qianchuan，将在第一个线程完成第二页后获取手机号码
2025-08-04 17:50:10 [信息] 多线程服务初始化完成
2025-08-04 17:50:10 [信息] 数据分配完成：共15条数据分配给3个线程
2025-08-04 17:50:10 [信息] 线程1分配到5条数据
2025-08-04 17:50:10 [信息] 线程2分配到5条数据
2025-08-04 17:50:10 [信息] 线程3分配到5条数据
2025-08-04 17:50:10 [信息] 屏幕工作区域: 1280x672
2025-08-04 17:50:10 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 17:50:10 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 17:50:10 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:50:10 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0), CPU=14核, RAM=6 GB
2025-08-04 17:50:10 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 17:50:10 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 17:50:10 [信息] 屏幕工作区域: 1280x672
2025-08-04 17:50:10 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 17:50:10 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 17:50:10 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:50:10 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_008, WebGL=ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=32 GB
2025-08-04 17:50:10 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 17:50:10 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 17:50:10 [信息] 屏幕工作区域: 1280x672
2025-08-04 17:50:10 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 17:50:10 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 17:50:10 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:50:10 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_006, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=64 GB
2025-08-04 17:50:10 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 17:50:10 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 17:50:10 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 17:50:10 [信息] 多线程注册启动成功，共3个线程
2025-08-04 17:50:10 线程1：[信息] 开始启动注册流程
2025-08-04 17:50:10 线程2：[信息] 开始启动注册流程
2025-08-04 17:50:10 线程3：[信息] 开始启动注册流程
2025-08-04 17:50:10 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 17:50:10 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 17:50:10 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 17:50:10 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 17:50:10 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 17:50:10 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 17:50:10 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 17:50:10 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 17:50:10 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 17:50:10 [信息] 多线程管理窗口已初始化
2025-08-04 17:50:10 [信息] UniformGrid列数已更新为: 1
2025-08-04 17:50:10 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 17:50:10 [信息] 多线程管理窗口已打开
2025-08-04 17:50:10 [信息] 多线程注册启动成功，共3个线程
2025-08-04 17:50:14 [信息] UniformGrid列数已更新为: 1
2025-08-04 17:50:14 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 17:50:14 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 17:50:14 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 17:50:14 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-04 17:50:14 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 17:50:15 [信息] UniformGrid列数已更新为: 1
2025-08-04 17:50:15 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 17:50:15 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 17:50:15 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 17:50:15 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 17:50:15 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 17:50:15 [信息] UniformGrid列数已更新为: 2
2025-08-04 17:50:15 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 17:50:15 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 17:50:15 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 17:50:15 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 17:50:15 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 17:50:16 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 17:50:16 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 17:50:16 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 17:50:18 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 17:50:18 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:50:18 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-04 17:50:18 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 17:50:18 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:50:18 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 17:50:18 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 17:50:18 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 17:50:18 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-04 17:50:18 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_006, CPU: 24核 (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器指纹注入: Canvas=canvas_fp_006, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=64 GB
2025-08-04 17:50:18 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 6核 (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=32 GB
2025-08-04 17:50:18 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 14核 (进度: 0%)
2025-08-04 17:50:18 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0), CPU=14核, RAM=6 GB
2025-08-04 17:50:20 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 17:50:20 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 17:50:20 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 17:50:22 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 64 GB
   • 平台信息: Win32
   • Do Not Track: manual

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7E8F9A0B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_006
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-C2D3E4F
   • MAC地址: 12-34-56-78-9A-BC
   • 屏幕分辨率: 1984x1021
   • 可用区域: 1984x981

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.70
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 17:50:22 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 6
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: manual

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7A8B9C0D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_002
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: A1-B2-C3-D4-E5-F6
   • 屏幕分辨率: 1720x995
   • 可用区域: 1720x955

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wifi
   • 电池API支持: True
   • 电池电量: 0.28
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 17:50:22 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 6    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: manual   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7A8B9C0D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_002    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: A1-B2-C3-D4-E5-F6    • 屏幕分辨率: 1720x995    • 可用区域: 1720x955   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wifi    • 电池API支持: True    • 电池电量: 0.28    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 17:50:22 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 64 GB    • 平台信息: Win32    • Do Not Track: manual   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7E8F9A0B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_006    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-C2D3E4F    • MAC地址: 12-34-56-78-9A-BC    • 屏幕分辨率: 1984x1021    • 可用区域: 1984x981   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.70    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 17:50:22 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 14
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: system

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7E8F9A0B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-C2D3E4F
   • MAC地址: 78-9A-BC-DE-F0-12
   • 屏幕分辨率: 1862x1050
   • 可用区域: 1862x1010

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A7B8C9D0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 3g
   • 电池API支持: True
   • 电池电量: 0.89
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 17:50:22 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 14    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: system   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7E8F9A0B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-C2D3E4F    • MAC地址: 78-9A-BC-DE-F0-12    • 屏幕分辨率: 1862x1050    • 可用区域: 1862x1010   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A7B8C9D0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 3g    • 电池API支持: True    • 电池电量: 0.89    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 17:50:22 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 17:50:22 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 17:50:22 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 17:50:22 线程3：[信息] 浏览器启动成功
2025-08-04 17:50:22 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 17:50:22 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 17:50:22 线程2：[信息] 浏览器启动成功
2025-08-04 17:50:22 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 17:50:23 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 17:50:23 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 17:50:23 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 17:50:23 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 17:50:23 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 17:50:23 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 17:50:23 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 17:50:23 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 17:50:23 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 17:50:23 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 17:50:23 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 17:50:23 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 17:50:23 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 17:50:23 线程1：[信息] 浏览器启动成功
2025-08-04 17:50:23 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 17:50:23 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 17:50:24 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 17:50:24 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 17:50:24 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 17:50:24 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 17:50:24 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 17:50:24 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 17:50:24 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 17:50:24 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 17:50:24 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 17:50:24 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 17:50:24 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 17:50:24 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 17:50:24 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 17:50:54 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-04 17:50:54 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 17:50:54 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 17:50:54 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 17:50:54 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 17:50:54 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 17:50:54 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 17:50:54 [信息] 第一页相关失败，数据保持不动
2025-08-04 17:50:54 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 17:50:54 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 17:50:54 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 17:50:54 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 17:50:54 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 17:50:54 [信息] 第一页相关失败，数据保持不动
2025-08-04 17:50:54 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 17:50:54 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 17:50:57 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 17:50:57 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 17:50:57 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 17:50:57 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 17:50:57 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:50:57 线程3：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-04 17:50:57 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 17:50:57 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 17:50:57 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 17:50:57 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:51:02 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 17:51:02 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 17:51:02 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 17:51:02 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 17:51:02 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 17:51:02 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 17:51:02 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 17:51:02 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 17:51:03 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 17:51:03 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 17:51:03 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 17:51:03 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 17:51:03 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 17:51:03 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 17:51:03 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 17:51:03 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 17:51:03 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 17:51:03 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 17:51:03 线程1：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 17:51:03 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 17:51:03 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 17:51:03 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 17:51:03 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 17:51:03 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 17:51:03 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 17:51:03 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 17:51:03 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 17:51:03 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 17:51:03 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 17:51:03 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 17:51:03 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 17:51:03 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 17:51:04 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 17:51:04 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 17:51:07 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 17:51:07 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 17:51:07 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 17:51:07 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 17:51:07 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 17:51:07 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:51:07 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 17:51:07 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 17:51:07 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 17:51:07 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 17:51:07 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:51:07 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 17:51:08 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35273 字节 (进度: 100%)
2025-08-04 17:51:08 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35273字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:51:08 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:51:09 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 17:51:09 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 17:51:09 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:51:09 线程1：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 17:51:09 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 17:51:09 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 17:51:09 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 17:51:09 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:51:09 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 17:51:09 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 17:51:09 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 17:51:09 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 17:51:09 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 17:51:09 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:51:13 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"ghm88n"},"taskId":"8ee8c6dc-7118-11f0-948a-9ab6db23b9b7"} (进度: 100%)
2025-08-04 17:51:13 线程3：[信息] [信息] 第一页第1次识别结果: ghm88n → 转换为小写: ghm88n (进度: 100%)
2025-08-04 17:51:14 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:51:14 线程3：[信息] [信息] 已填入验证码: ghm88n (进度: 100%)
2025-08-04 17:51:14 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:51:14 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35900 字节 (进度: 100%)
2025-08-04 17:51:14 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35900字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:51:14 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:51:15 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35627 字节 (进度: 100%)
2025-08-04 17:51:15 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35627字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:51:15 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:51:15 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"a4zbn7"},"taskId":"9026f618-7118-11f0-b791-029e46bc98e3"} (进度: 100%)
2025-08-04 17:51:15 线程2：[信息] [信息] 第一页第1次识别结果: a4zbn7 → 转换为小写: a4zbn7 (进度: 100%)
2025-08-04 17:51:15 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:51:16 线程2：[信息] [信息] 已填入验证码: a4zbn7 (进度: 100%)
2025-08-04 17:51:16 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:51:16 线程3：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 17:51:16 线程3：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 17:51:16 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 17:51:16 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 17:51:16 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 17:51:16 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 17:51:16 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 17:51:16 线程3：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 17:51:16 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 17:51:16 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 17:51:16 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 17:51:18 线程2：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 17:51:18 线程2：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-08-04 17:51:18 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 17:51:18 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 17:51:18
2025-08-04 17:51:18 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"bmtx7c"},"taskId":"91cd375c-7118-11f0-ae6e-62c5329370b7"} (进度: 100%)
2025-08-04 17:51:18 线程1：[信息] [信息] 第一页第1次识别结果: bmtx7c → 转换为小写: bmtx7c (进度: 100%)
2025-08-04 17:51:18 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:51:18 线程1：[信息] [信息] 已填入验证码: bmtx7c (进度: 100%)
2025-08-04 17:51:19 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:51:20 线程2：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:51:21 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 17:51:21 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 17:51:21 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 17:51:21 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 17:51:21 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 17:51:21 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 17:51:21 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 17:51:21 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 17:51:21 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 17:51:21 线程1：[信息] 已继续
2025-08-04 17:51:21 [信息] 线程1已继续
2025-08-04 17:51:21 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 17:51:21 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 17:51:21
2025-08-04 17:51:23 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 17:51:23 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 17:51:23
2025-08-04 17:51:23 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 30625 字节 (进度: 100%)
2025-08-04 17:51:23 线程2：[信息] [信息] ✅ 图片验证通过：200x71px，30625字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:51:23 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:51:24 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 17:51:24 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 17:51:24
2025-08-04 17:51:26 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 17:51:26 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 17:51:26
2025-08-04 17:51:26 [信息] [线程3] 邮箱验证码获取成功: 873892，立即停止重复请求
2025-08-04 17:51:26 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 17:51:26 [信息] [线程3] 已清理响应文件
2025-08-04 17:51:26 线程3：[信息] [信息] 验证码获取成功: 873892，正在自动填入... (进度: 25%)
2025-08-04 17:51:26 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 17:51:26 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 17:51:26 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 17:51:26 [信息] 线程3完成第二页事件已处理
2025-08-04 17:51:26 [信息] 线程3完成第二页，开始批量获取手机号码...
2025-08-04 17:51:26 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 17:51:26 线程3：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 35%)
2025-08-04 17:51:26 [信息] 开始批量获取3个手机号码，服务商: Qianchuan
2025-08-04 17:51:26 [信息] 千川API：不在初始化时获取手机号码，将在各线程第二页完成后获取
2025-08-04 17:51:26 [信息] 批量获取3个手机号码成功
2025-08-04 17:51:26 线程3：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 48%)
2025-08-04 17:51:26 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 17:51:26 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 17:51:29 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 17:51:29 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 17:51:29
2025-08-04 17:51:30 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 48%)
2025-08-04 17:51:30 线程3：[信息] [信息] 开始填写密码信息... (进度: 48%)
2025-08-04 17:51:30 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 17:51:30 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 17:51:30 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 48%)
2025-08-04 17:51:31 [信息] [线程1] 邮箱验证码获取成功: 492326，立即停止重复请求
2025-08-04 17:51:31 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 48%)
2025-08-04 17:51:31 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 17:51:31 [信息] [线程1] 已清理响应文件
2025-08-04 17:51:31 线程1：[信息] [信息] 验证码获取成功: 492326，正在自动填入... (进度: 100%)
2025-08-04 17:51:31 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 17:51:31 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 17:51:31 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 17:51:31 [信息] 线程1完成第二页事件已处理
2025-08-04 17:51:31 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-04 17:51:31 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 17:51:31 线程1：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 100%)
2025-08-04 17:51:31 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 17:51:31 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 17:51:31 线程1：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 100%)
2025-08-04 17:51:32 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870316571","phoneId":"3549116571840","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5824485,"phoneNo":"4367870316571","projectId":804413,"startTime":"2025-08-04 17:51:32","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"a6cec00e-3f62-47ac-a45d-e2c40c36bfd8"}
2025-08-04 17:51:32 [信息] [千川API] 获取手机号码成功: +4367870316571
2025-08-04 17:51:34 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 48%)
2025-08-04 17:51:34 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 48%)
2025-08-04 17:51:34 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 48%)
2025-08-04 17:51:34 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 17:51:34 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 17:51:34 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 17:51:34 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 17:51:34 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 17:51:34 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4368054002135","phoneId":"3549402135910","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5824486,"phoneNo":"4368054002135","projectId":804413,"startTime":"2025-08-04 17:51:34","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"8e86e3a2-2e7d-496b-aaf5-cbd69ac8b31e"}
2025-08-04 17:51:34 [信息] [千川API] 获取手机号码成功: +4368054002135
2025-08-04 17:51:35 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 17:51:38 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 17:51:38 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 17:51:38 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 17:51:39 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 48%)
2025-08-04 17:51:39 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 48%)
2025-08-04 17:51:45 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-04 17:51:45 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-04 17:52:03 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 48%)
2025-08-04 17:52:03 [警告] 线程3未找到分配的手机号码，服务商: Qianchuan
2025-08-04 17:52:03 线程3：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 48%)
2025-08-04 17:52:03 线程3：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 48%)
2025-08-04 17:52:03 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 17:52:03 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 48%)
2025-08-04 17:52:04 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 48%)
2025-08-04 17:52:05 线程3：[信息] [信息] 已选择国家: Chile (进度: 48%)
2025-08-04 17:52:05 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 48%)
2025-08-04 17:52:05 线程3：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-04 17:52:05 线程3：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-04 17:52:06 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-04 17:52:06 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870319273","phoneId":"3552519273469","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5824497,"phoneNo":"4367870319273","projectId":804413,"startTime":"2025-08-04 17:52:06","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"a019ed68-c497-481f-bee4-3334d8f892d3"}
2025-08-04 17:52:06 [信息] [千川API] 获取手机号码成功: +4367870319273
2025-08-04 17:52:07 线程3：[信息] [信息] 已选择国家代码 +43 (进度: 48%)
2025-08-04 17:52:08 线程3：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 48%)
2025-08-04 17:52:08 线程3：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 48%)
2025-08-04 17:52:08 线程3：[信息] [信息] 千川手机号码获取成功: +4367870316571 (进度: 48%)
2025-08-04 17:52:08 线程3：[信息] [信息] 千川手机号码已自动填入: +4367870316571 (进度: 48%)
2025-08-04 17:52:09 线程3：[信息] [信息] 使用已保存的手机号码: 67870316571 (进度: 48%)
2025-08-04 17:52:09 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 48%)
2025-08-04 17:52:10 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-04 17:52:10 [警告] 线程1未找到分配的手机号码，服务商: Qianchuan
2025-08-04 17:52:10 线程1：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 100%)
2025-08-04 17:52:10 线程1：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 100%)
2025-08-04 17:52:10 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 17:52:10 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 17:52:11 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 17:52:12 线程3：[信息] [信息] 进入付款信息页面... (进度: 48%)
2025-08-04 17:52:12 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870315491","phoneId":"************","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5824499,"phoneNo":"4367870315491","projectId":804413,"startTime":"2025-08-04 17:52:12","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"2a54467b-e12f-4339-a16b-ecd8c8d43ff8"}
2025-08-04 17:52:12 [信息] [千川API] 获取手机号码成功: +4367870315491
2025-08-04 17:52:12 线程1：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 17:52:12 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 17:52:12 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 17:52:12 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 17:52:12 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 17:52:13 线程3：[信息] [信息] 正在选择月份: May (进度: 48%)
2025-08-04 17:52:13 线程3：[信息] [信息] 已选择月份（标准选项）: May (进度: 48%)
2025-08-04 17:52:14 线程1：[信息] [信息] 已选择国家代码 +43 (进度: 100%)
2025-08-04 17:52:14 线程3：[信息] [信息] 正在选择年份: 2029 (进度: 48%)
2025-08-04 17:52:14 线程3：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 48%)
2025-08-04 17:52:14 线程1：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 100%)
2025-08-04 17:52:14 线程1：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 100%)
2025-08-04 17:52:14 线程1：[信息] [信息] 千川手机号码获取成功: +4368054002135 (进度: 100%)
2025-08-04 17:52:14 线程1：[信息] [信息] 千川手机号码已自动填入: +4368054002135 (进度: 100%)
2025-08-04 17:52:14 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 48%)
2025-08-04 17:52:14 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 48%)
2025-08-04 17:52:14 线程3：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-04 17:52:14 线程3：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-04 17:52:15 线程1：[信息] [信息] 使用已保存的手机号码: 68054002135 (进度: 100%)
2025-08-04 17:52:15 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 17:52:18 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 17:52:19 线程1：[信息] [信息] 正在选择月份: December (进度: 100%)
2025-08-04 17:52:19 线程1：[信息] [信息] 已选择月份（标准选项）: December (进度: 100%)
2025-08-04 17:52:20 线程1：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-04 17:52:20 线程1：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 100%)
2025-08-04 17:52:20 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-04 17:52:21 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 17:52:21 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 17:52:21 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 17:52:21 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 17:52:21 线程3：[信息] [信息] 已选择国家代码: +43 (进度: 48%)
2025-08-04 17:52:21 线程3：[信息] [信息] 已清空并重新填写手机号码: 67870316571 (进度: 48%)
2025-08-04 17:52:21 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 48%)
2025-08-04 17:52:23 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 48%)
2025-08-04 17:52:24 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 17:52:24 线程3：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 17:52:24 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 17:52:25 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 17:52:26 线程1：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-04 17:52:26 线程1：[信息] [信息] 已清空并重新填写手机号码: 68054002135 (进度: 100%)
2025-08-04 17:52:27 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 17:52:27 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 17:52:27 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:52:29 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 17:52:29 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 17:52:29 线程1：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 17:52:29 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 17:52:31 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34526 字节 (进度: 100%)
2025-08-04 17:52:31 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34526字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:52:31 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:52:32 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 17:52:32 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:52:33 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"mwrynz"},"taskId":"be4c0e0c-7118-11f0-9f1c-62c5329370b7"} (进度: 100%)
2025-08-04 17:52:33 线程3：[信息] [信息] 第六页第1次识别结果: mwrynz → 转换为小写: mwrynz (进度: 100%)
2025-08-04 17:52:33 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:52:33 线程3：[信息] [信息] 第六页已填入验证码: mwrynz (进度: 100%)
2025-08-04 17:52:33 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:52:37 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 17:52:37 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 17:52:37 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 32892 字节 (进度: 100%)
2025-08-04 17:52:37 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，32892字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:52:37 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:52:40 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 17:52:40 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"zhff4zt"},"taskId":"c29c36bc-7118-11f0-b896-029e46bc98e3"} (进度: 100%)
2025-08-04 17:52:40 线程1：[信息] [信息] 第六页第1次识别结果: zhff4zt → 转换为小写: zhff4zt (进度: 100%)
2025-08-04 17:52:40 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:52:40 线程1：[信息] [信息] 第六页已填入验证码: zhff4zt (进度: 100%)
2025-08-04 17:52:40 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:52:43 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 17:52:43 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 17:52:43 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 17:52:43 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 17:52:44 线程1：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 17:52:44 线程1：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-04 17:52:46 线程1：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:52:48 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-04 17:52:48 线程3：[信息] [信息] 线程3等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-04 17:52:50 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31397 字节 (进度: 100%)
2025-08-04 17:52:50 线程1：[信息] [信息] ✅ 图片验证通过：200x70px，31397字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:52:50 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:52:53 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"c57s54"},"taskId":"ca042d1a-7118-11f0-ae6e-62c5329370b7"} (进度: 100%)
2025-08-04 17:52:53 线程1：[信息] [信息] 第六页第2次识别结果: c57s54 → 转换为小写: c57s54 (进度: 100%)
2025-08-04 17:52:53 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:52:53 线程1：[信息] [信息] 第六页已填入验证码: c57s54 (进度: 100%)
2025-08-04 17:52:53 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:52:53 线程3：[信息] [信息] 线程3第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-04 17:52:53 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 17:52:53 [信息] [千川API] 获取验证码请求: 4367870316571
2025-08-04 17:52:55 [信息] [千川API] 验证码响应: {"data":{"code":"0157","message":"ok","modle":"[AMAZON] 0157"},"msg":"操作成功","status":200,"success":true,"t":"691b97db-c1a4-42fd-acef-7dcdc1f98546"}
2025-08-04 17:52:55 [信息] [千川API] 获取验证码成功: 0157
2025-08-04 17:52:55 线程3：[信息] [信息] 线程3千川验证码获取成功: 0157 (进度: 100%)
2025-08-04 17:52:55 线程3：[信息] [信息] 线程3验证码获取成功: 0157，立即填入验证码... (进度: 100%)
2025-08-04 17:52:55 [信息] 线程3手机号码已加入释放队列: +4367870316571 (原因: 验证码获取成功)
2025-08-04 17:52:55 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 17:52:55 [错误] 手机API服务未设置，无法释放手机号码
2025-08-04 17:52:55 [信息] 定时批量释放完成: 手机API服务未设置
2025-08-04 17:52:55 线程3：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 17:52:55 线程3：[信息] [信息] 线程3已自动填入手机验证码: 0157 (进度: 100%)
2025-08-04 17:52:56 线程1：[信息] [信息] 第2次图形验证码识别成功 (进度: 100%)
2025-08-04 17:52:56 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 17:52:56 线程3：[信息] [信息] 线程3正在自动点击Continue按钮... (进度: 100%)
2025-08-04 17:52:56 线程3：[信息] [信息] 线程3手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 17:52:59 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 17:52:59 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 17:52:59 线程3：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 17:53:00 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 17:53:02 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 17:53:02 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 17:53:02 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 17:53:02 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 17:53:03 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 17:53:03 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 17:53:07 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-04 17:53:07 线程1：[信息] [信息] 线程1等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-04 17:53:09 线程3：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 17:53:09 线程3：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 17:53:09 线程3：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 17:53:12 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 17:53:12 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 17:53:12 线程2：[信息] 已暂停
2025-08-04 17:53:12 [信息] 线程2已暂停
2025-08-04 17:53:12 [信息] 线程2已暂停
2025-08-04 17:53:12 线程1：[信息] [信息] 线程1第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-04 17:53:12 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 17:53:12 [信息] [千川API] 获取验证码请求: 4368054002135
2025-08-04 17:53:12 [信息] [千川API] 验证码响应: {"data":{"code":"9963","message":"ok","modle":"[AMAZON] 9963"},"msg":"操作成功","status":200,"success":true,"t":"b03cd120-f612-4b6a-95dd-f10179a79293"}
2025-08-04 17:53:12 [信息] [千川API] 获取验证码成功: 9963
2025-08-04 17:53:12 线程1：[信息] [信息] 线程1千川验证码获取成功: 9963 (进度: 100%)
2025-08-04 17:53:12 线程1：[信息] [信息] 线程1验证码获取成功: 9963，立即填入验证码... (进度: 100%)
2025-08-04 17:53:12 [信息] 线程1手机号码已加入释放队列: +4368054002135 (原因: 验证码获取成功)
2025-08-04 17:53:12 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 17:53:12 线程1：[信息] [信息] 线程1已自动填入手机验证码: 9963 (进度: 100%)
2025-08-04 17:53:13 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-04 17:53:13 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 17:53:16 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 17:53:16 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 17:53:17 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 17:53:20 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 17:53:20 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 17:53:23 线程2：[信息] [信息] 第一页第2次识别失败: API调用异常: The request was canceled due to the configured HttpClient.Timeout of 120 seconds elapsing. (进度: 100%)
2025-08-04 17:53:23 线程2：[信息] [信息] 检测到注册已暂停或终止，停止第一页图形验证码处理 (进度: 100%)
2025-08-04 17:53:23 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 17:53:23 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 17:53:23 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 2 (进度: 100%)
2025-08-04 17:53:23 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 17:53:23 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 17:53:23 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 17:53:27 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 17:53:27 [错误] 手机API服务未设置，无法释放手机号码
2025-08-04 17:53:27 [信息] 定时批量释放完成: 手机API服务未设置
2025-08-04 17:53:27 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 17:53:27 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 17:53:27 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] 已继续
2025-08-04 17:53:27 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 17:53:27 [信息] 线程2已继续
2025-08-04 17:53:27 [信息] [线程2] 已删除旧的响应文件
2025-08-04 17:53:27 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 17:53:27 [信息] 继续了 2 个可继续的线程
2025-08-04 17:53:27 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify' → 第2页 (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 🔍 疑似第2页，进行二次确认... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] ✅ 确认为第2页：找到Verification code输入框 (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息]  智能检测到当前在第2页 (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify' → 第2页 (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 🔍 疑似第2页，进行二次确认... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] ✅ 确认为第2页：找到Verification code输入框 (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 智能检测到当前在第2页，继续执行... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 点击验证按钮... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 验证码验证完成，等待页面跳转... (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 已彻底取消邮箱验证码获取线程 (进度: 100%)
2025-08-04 17:53:27 [信息] 线程2完成第二页事件已处理
2025-08-04 17:53:27 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-04 17:53:27 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 17:53:27 线程2：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 100%)
2025-08-04 17:53:27 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 17:53:27 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 17:53:27 线程2：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 100%)
2025-08-04 17:53:29 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 17:53:29 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:53:29
2025-08-04 17:53:30 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 17:53:30 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 17:53:32 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 17:53:32 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:53:32
2025-08-04 17:53:33 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870310029","phoneId":"3561310029578","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5824520,"phoneNo":"4367870310029","projectId":804413,"startTime":"2025-08-04 17:53:33","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"268c7a7f-87c5-4f1c-bcce-9f6dba896f7f"}
2025-08-04 17:53:33 [信息] [千川API] 获取手机号码成功: +4367870310029
2025-08-04 17:53:35 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 17:53:35 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 17:53:35
2025-08-04 17:53:36 [信息] [线程2] 邮箱验证码获取成功: 975715，立即停止重复请求
2025-08-04 17:53:36 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 17:53:36 [信息] [线程2] 已清理响应文件
2025-08-04 17:53:36 线程2：[信息] [信息] 验证码获取成功: 975715，正在自动填入... (进度: 100%)
2025-08-04 17:53:36 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 17:53:36 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 17:53:36 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 17:53:36 [信息] 线程2完成第二页事件已处理
2025-08-04 17:53:36 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-04 17:53:36 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 17:53:37 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 17:53:37 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 17:53:37 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 17:53:38 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 17:53:39 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 17:53:39 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 17:53:39 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 17:53:39 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 17:53:39 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 17:53:40 线程3：[信息] [信息] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程... (进度: 100%)
2025-08-04 17:53:40 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-04 17:53:40 线程3：[信息] [信息] 🔍 智能检测更多按钮... (进度: 100%)
2025-08-04 17:53:40 线程3：[信息] [信息] 🔍 第1次检查更多按钮... (进度: 100%)
2025-08-04 17:53:41 线程3：[信息] [信息] ⚠️ 第1次检查未找到更多按钮 (进度: 100%)
2025-08-04 17:53:41 [信息] 第1次检查未找到更多按钮
2025-08-04 17:53:41 线程3：[信息] [信息] ⏳ 等待3秒后重新检查... (进度: 100%)
2025-08-04 17:53:41 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 17:53:41 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 17:53:41 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 17:53:44 线程3：[信息] [信息] 🔍 第2次检查更多按钮... (进度: 100%)
2025-08-04 17:53:45 线程3：[信息] [信息] ⚠️ 第2次检查未找到更多按钮 (进度: 100%)
2025-08-04 17:53:45 [信息] 第2次检查未找到更多按钮
2025-08-04 17:53:45 线程3：[信息] [信息] ⏳ 等待3秒后重新检查... (进度: 100%)
2025-08-04 17:53:47 线程1：[信息] [信息] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程... (进度: 100%)
2025-08-04 17:53:47 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-04 17:53:47 线程1：[信息] [信息] 🔍 智能检测更多按钮... (进度: 100%)
2025-08-04 17:53:47 线程1：[信息] [信息] 🔍 第1次检查更多按钮... (进度: 100%)
2025-08-04 17:53:48 线程3：[信息] [信息] 🔍 第3次检查更多按钮... (进度: 100%)
2025-08-04 17:53:48 线程1：[信息] [信息] ⚠️ 第1次检查未找到更多按钮 (进度: 100%)
2025-08-04 17:53:48 [信息] 第1次检查未找到更多按钮
2025-08-04 17:53:48 线程1：[信息] [信息] ⏳ 等待3秒后重新检查... (进度: 100%)
2025-08-04 17:53:49 线程3：[信息] [信息] ⚠️ 第3次检查未找到更多按钮 (进度: 100%)
2025-08-04 17:53:49 [信息] 第3次检查未找到更多按钮
2025-08-04 17:53:49 线程3：[信息] [信息] ❌ 3次检查均未找到更多按钮 (进度: 100%)
2025-08-04 17:53:49 [信息] 3次检查均未找到更多按钮，控制台按钮处理失败
2025-08-04 17:53:49 线程3：[信息] [信息] ⚠️ 进入密钥提取页面超时，请等待页面加载完成后点击继续注册 (进度: 100%)
2025-08-04 17:53:49 [信息] 进入密钥提取页面超时，暂停注册等待用户手动继续
2025-08-04 17:53:49 线程3：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 17:53:50 线程2：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[type='submit'], input[type='submit']") to be visible (进度: 100%)
2025-08-04 17:53:51 线程1：[信息] [信息] 🔍 第2次检查更多按钮... (进度: 100%)
2025-08-04 17:53:51 线程1：[信息] [信息] ✅ 第2次检查成功找到更多按钮 (进度: 100%)
2025-08-04 17:53:51 [信息] 第2次检查成功找到更多按钮
2025-08-04 17:53:51 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 17:53:51 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 17:53:51 [信息] 成功点击更多按钮
2025-08-04 17:53:52 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 17:53:53 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 17:53:53 [信息] 成功点击账户信息按钮
2025-08-04 17:53:54 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 17:53:54 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 17:53:54 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 17:53:54 [信息] 成功定位到'安全凭证'链接
2025-08-04 17:53:59 线程2：[信息] [信息] 第3.5页执行失败: Timeout 15000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Choose paid plan" }) to be visible (进度: 100%)
2025-08-04 17:53:59 线程2：[信息] 已继续
2025-08-04 17:53:59 [信息] 线程2已继续
2025-08-04 17:54:02 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-04 17:54:02 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 17:54:02 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 17:54:02 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 17:54:02 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Choose paid plan' → 第3页 (进度: 100%)
2025-08-04 17:54:02 线程2：[信息] [信息] 检测到第3.5页（账户类型确认页面），直接点击Choose paid plan按钮... (进度: 100%)
2025-08-04 17:54:02 线程2：[信息] [信息] 账户类型确认完成，进入第4页（联系信息页面）... (进度: 100%)
2025-08-04 17:54:07 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 17:54:07 [信息] 成功点击'安全凭证'链接
2025-08-04 17:54:07 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 17:54:10 线程1：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-04 17:54:10 线程1：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-04 17:54:10 线程1：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-04 17:54:10 [信息] 检测到账单问题，开始处理
2025-08-04 17:54:10 线程1：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：TaI5e0oIo37 ③AWS密码：fHm2MNpo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-04 17:54:10 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：TaI5e0oIo37 ③AWS密码：fHm2MNpo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-04 17:54:10 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：TaI5e0oIo37 ③AWS密码：fHm2MNpo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-04 17:54:10 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：TaI5e0oIo37 ③AWS密码：fHm2MNpo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-04 17:54:10 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：TaI5e0oIo37 ③AWS密码：fHm2MNpo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-04 17:54:10 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：TaI5e0oIo37 ③AWS密码：fHm2MNpo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-04 17:54:10 线程1：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-04 17:54:10 线程1：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-04 17:54:10 [信息] 注册完成 - 账单提示处理
2025-08-04 17:54:10 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 17:54:10 线程1：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 17:54:10 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-04 17:54:10 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-04 17:54:10 [信息] 已完成账单问题数据移除: <EMAIL>
2025-08-04 17:54:10 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-04 17:54:10 线程1：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-04 17:54:10 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 17:54:27 线程2：[信息] [信息] 检测到注册已暂停或终止，停止后续操作 (进度: 100%)
2025-08-04 17:54:27 线程2：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 100%)
2025-08-04 17:54:27 线程2：[信息] [信息] 🔬 执行详细页面分析... (进度: 100%)
2025-08-04 17:54:27 线程2：[信息] [信息] 📄 页面URL: https://portal.aws.amazon.com/billing/signup?type=register&refid=em_127222&p=free&c=hp&z=1&redirect_url=https%3A%2F%2Faws.amazon.com%2Fregistration-confirmation#/account (进度: 100%)
2025-08-04 17:54:27 线程2：[信息] [信息] 📋 页面标题: AWS Console - Signup (进度: 100%)
2025-08-04 17:54:29 线程2：[信息] [信息] 📊 分析结果: 第四页-联系信息(3/4个元素匹配), 第六页-手机验证(1/3个元素匹配) (进度: 100%)
2025-08-04 17:54:29 线程2：[信息] [信息]  智能检测到当前在第6页 (进度: 100%)
2025-08-04 17:54:29 线程2：[信息] [信息] 智能检测到当前在第6页，开始智能处理... (进度: 100%)
2025-08-04 17:54:29 线程2：[信息] [信息] 检测第6页手机验证状态... (进度: 100%)
2025-08-04 17:54:59 线程2：[信息] [信息] 第6页暂停处理失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Mobile phone number" }) (进度: 100%)
2025-08-04 17:54:59 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 17:54:59 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 17:54:59 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 17:55:11 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 17:55:11 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 17:55:11 线程3：[信息] 已暂停
2025-08-04 17:55:11 [信息] 线程3已暂停
2025-08-04 17:55:11 [信息] 线程3已暂停
2025-08-04 17:55:12 线程3：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 7 (进度: 100%)
2025-08-04 17:55:12 线程3：[信息] [信息] 继续密钥提取流程，从点击更多按钮开始... (进度: 100%)
2025-08-04 17:55:12 [信息] 继续密钥提取流程，从点击更多按钮开始
2025-08-04 17:55:12 线程3：[信息] [信息] 🔗 继续密钥提取流程，从点击更多按钮开始... (进度: 100%)
2025-08-04 17:55:12 [信息] 继续密钥提取流程，从点击更多按钮开始
2025-08-04 17:55:12 线程3：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 17:55:15 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 17:55:15 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 17:55:15 线程3：[信息] 已暂停
2025-08-04 17:55:15 [信息] 线程3已暂停
2025-08-04 17:55:15 [信息] 线程3已暂停
2025-08-04 17:55:17 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 17:55:17 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 17:55:17 线程2：[信息] 已暂停
2025-08-04 17:55:17 [信息] 线程2已暂停
2025-08-04 17:55:17 [信息] 线程2已暂停
2025-08-04 17:55:17 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 6 (进度: 100%)
2025-08-04 17:55:17 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 17:55:17 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 17:55:17 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 17:55:17 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 100%)
2025-08-04 17:55:17 线程2：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-04 17:55:17 线程2：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-04 17:55:17 线程2：[信息] [信息] 智能检测到当前在第4页，开始智能处理... (进度: 100%)
2025-08-04 17:55:17 [警告] 线程2未找到分配的手机号码，服务商: Qianchuan
2025-08-04 17:55:17 线程2：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 100%)
2025-08-04 17:55:17 线程2：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 100%)
2025-08-04 17:55:17 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 17:55:18 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 17:55:18 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 17:55:20 线程2：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 17:55:20 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 17:55:20 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 17:55:20 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 17:55:20 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 17:55:21 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870317663","phoneId":"3571917663799","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5824547,"phoneNo":"4367870317663","projectId":804413,"startTime":"2025-08-04 17:55:20","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"63c4f1ad-8932-413e-8f8e-3642e4d0bb2e"}
2025-08-04 17:55:21 [信息] [千川API] 获取手机号码成功: +4367870317663
2025-08-04 17:55:23 线程3：[信息] [信息] ❌ 继续密钥提取流程失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("awsc-nav-more-menu") to be visible
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button> (进度: 100%)
2025-08-04 17:55:23 [信息] 继续密钥提取流程失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("awsc-nav-more-menu") to be visible
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
  -   locator resolved to hidden <button title="Más" aria-expanded="false" data-testid="a…>…</button>
2025-08-04 17:55:23 线程3：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 17:55:23 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 17:55:23 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：Fnu4072Ml ③AWS密码：CjIC2ydn ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 17:55:23 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：Fnu4072Ml ③AWS密码：CjIC2ydn ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:55:23 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：Fnu4072Ml ③AWS密码：CjIC2ydn ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:55:23 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：Fnu4072Ml ③AWS密码：CjIC2ydn ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:55:23 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：Fnu4072Ml ③AWS密码：CjIC2ydn ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:55:23 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：Fnu4072Ml ③AWS密码：CjIC2ydn ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 17:55:23 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 17:55:23 线程3：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 17:55:23 线程3：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 17:55:23 [信息] 注册完成（密钥提取失败）
2025-08-04 17:55:23 线程3：[信息] 已继续
2025-08-04 17:55:23 [信息] 线程3已继续
2025-08-04 17:55:23 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 17:55:23 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 17:55:23 [信息] 已完成数据移除: <EMAIL>
2025-08-04 17:55:23 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 17:55:24 线程3：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 17:55:24 [信息] 线程3数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 17:55:25 线程2：[信息] [信息] 已选择国家代码 +43 (进度: 100%)
2025-08-04 17:55:25 线程2：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 100%)
2025-08-04 17:55:25 线程2：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 100%)
2025-08-04 17:55:25 线程2：[信息] [信息] 千川手机号码获取成功: +4367870310029 (进度: 100%)
2025-08-04 17:55:25 线程2：[信息] [信息] 千川手机号码已自动填入: +4367870310029 (进度: 100%)
2025-08-04 17:55:26 线程2：[信息] [信息] 使用已保存的手机号码: 67870310029 (进度: 100%)
2025-08-04 17:55:26 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 17:55:29 线程2：[信息] [信息] 选择国家代码失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Country or region code" }) (进度: 100%)
2025-08-04 17:55:30 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 17:55:30 线程2：[信息] [信息] 正在选择月份: April (进度: 100%)
2025-08-04 17:55:31 线程2：[信息] [信息] 已选择月份（标准选项）: April (进度: 100%)
2025-08-04 17:55:31 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 100%)
2025-08-04 17:55:32 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 100%)
2025-08-04 17:55:32 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 17:55:32 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 17:55:32 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 17:55:32 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 17:55:38 线程2：[信息] [信息] 已清空并重新填写手机号码: 67870310029 (进度: 100%)
2025-08-04 17:55:38 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 17:55:38 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 17:55:40 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 17:55:40 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 17:55:40 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 17:55:40 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 17:55:43 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 17:55:43 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:55:45 线程2：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-04 17:55:45 线程2：[信息] [信息] 已清空并重新填写手机号码: 67870310029 (进度: 100%)
2025-08-04 17:55:45 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 17:55:47 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 17:55:47 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 17:55:47 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 17:55:47 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 17:55:50 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 17:55:50 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 17:55:56 线程2：[信息] [信息] 第六页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-04 17:55:56 线程2：[信息] [信息] 第六页第1次识别异常: 验证码元素等待超时，需要手动处理 (进度: 100%)
2025-08-04 17:55:56 线程2：[信息] [信息] 第六页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-04 17:55:56 线程2：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 17:55:56 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 17:55:56 线程2：[信息] 已继续
2025-08-04 17:55:56 [信息] 线程2已继续
2025-08-04 17:55:58 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35469 字节 (进度: 100%)
2025-08-04 17:55:58 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35469字节，复杂度符合要求 (进度: 100%)
2025-08-04 17:55:58 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 17:56:02 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"psw5tz"},"taskId":"3ac4ecba-7119-11f0-b791-029e46bc98e3"} (进度: 100%)
2025-08-04 17:56:02 线程2：[信息] [信息] 第六页第1次识别结果: psw5tz → 转换为小写: psw5tz (进度: 100%)
2025-08-04 17:56:02 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 17:56:02 线程2：[信息] [信息] 第六页已填入验证码: psw5tz (进度: 100%)
2025-08-04 17:56:02 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 17:56:05 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 17:56:05 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 17:56:08 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 17:56:11 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 17:56:11 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 17:56:11 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 17:56:11 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 17:56:16 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 17:56:16 线程2：[信息] [信息] 线程2等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-04 17:56:22 线程2：[信息] [信息] 线程2第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-04 17:56:22 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 17:56:22 [信息] [千川API] 获取验证码请求: 4367870310029
2025-08-04 17:56:23 [信息] [千川API] 验证码响应: {"data":{"code":"2001","message":"ok","modle":"[AMAZON] 2001"},"msg":"操作成功","status":200,"success":true,"t":"a52ec28d-0295-44ab-8a51-44398fbe9272"}
2025-08-04 17:56:23 [信息] [千川API] 获取验证码成功: 2001
2025-08-04 17:56:23 线程2：[信息] [信息] 线程2千川验证码获取成功: 2001 (进度: 100%)
2025-08-04 17:56:23 线程2：[信息] [信息] 线程2验证码获取成功: 2001，立即填入验证码... (进度: 100%)
2025-08-04 17:56:23 [信息] 线程2手机号码已加入释放队列: +4367870310029 (原因: 验证码获取成功)
2025-08-04 17:56:23 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 17:56:23 线程2：[信息] [信息] 线程2已自动填入手机验证码: 2001 (进度: 100%)
2025-08-04 17:56:24 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-04 17:56:24 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 17:56:25 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 17:56:25 [错误] 手机API服务未设置，无法释放手机号码
2025-08-04 17:56:25 [信息] 定时批量释放完成: 手机API服务未设置
2025-08-04 17:56:29 线程2：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-04 17:56:29 线程2：[信息] [信息] 线程2检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-04 17:56:29 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-04 17:56:29 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 17:56:29 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 17:56:29 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 17:56:29 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 17:56:29 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 17:56:29 线程2：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-04 17:56:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:56:29 [信息] 多线程状态已重置
2025-08-04 17:56:29 [信息] 线程2请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-04 17:56:29 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-04 17:56:29 [信息] 线程2失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-04 17:56:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:56:29 [信息] 多线程状态已重置
2025-08-04 17:56:29 线程2：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-04 17:56:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:56:29 [信息] 多线程状态已重置
2025-08-04 17:56:29 线程2：[信息] [信息] 线程2注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-04 17:56:29 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 0%)
2025-08-04 17:56:29 线程2：[信息] 已继续
2025-08-04 17:56:29 [信息] 线程2已继续
2025-08-04 17:56:45 [信息] 获取线程2当前数据: <EMAIL>
2025-08-04 17:56:45 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 17:56:45 线程2：[信息] 数据详情: <EMAIL>|2b3SgTg7|Vasquez Odette|CAP|Los pioneros 839 curauma|Valparaiso|Valparaiso|2340000|5331870057461829|04|27|191|Vasquez Odette|R4132d9Rq1T|CL
2025-08-04 17:56:45 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 17:56:45 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 98%)
2025-08-04 17:56:45 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:56:45 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:56:45 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:56:45 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:56:45 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:56:45 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:56:45 [信息] 多线程状态已重置
2025-08-04 17:56:45 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:56:45 [信息] 多线程状态已重置
2025-08-04 17:56:45 线程2：[信息] [信息] 注册已终止 (进度: 98%)
2025-08-04 17:56:45 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:56:45 [信息] 多线程状态已重置
2025-08-04 17:56:45 线程2：[信息] [信息] 所有自动线程已停止 (进度: 98%)
2025-08-04 17:56:45 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:56:45 [信息] 多线程状态已重置
2025-08-04 17:56:45 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 98%)
2025-08-04 17:56:45 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:56:45 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:56:45 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:56:45 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:56:45 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：R4132d9Rq1T ③AWS密码：2b3SgTg7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 17:56:45 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:56:45 [信息] 多线程状态已重置
2025-08-04 17:56:45 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 17:56:45 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250804_175010
2025-08-04 17:56:45 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 17:56:45 [信息] 多线程状态已重置
2025-08-04 17:56:45 线程2：[信息] 已终止
2025-08-04 17:56:45 [信息] 线程2已终止
2025-08-04 17:56:45 [信息] 开始处理线程2终止数据，共1个数据
2025-08-04 17:56:45 [信息] 处理线程2终止数据: <EMAIL>
2025-08-04 17:56:45 [信息] 线程2终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 17:56:45 [信息] 线程2终止数据处理完成，成功移动1个数据
2025-08-04 17:56:45 [信息] UniformGrid列数已更新为: 1
2025-08-04 17:56:45 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 17:56:45 [信息] 线程2已终止
2025-08-04 17:56:46 [信息] 多线程窗口引用已清理
2025-08-04 17:56:46 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 17:56:46 [信息] 多线程管理窗口正在关闭
2025-08-04 17:56:47 [信息] 程序正在退出，开始清理工作...
2025-08-04 17:56:47 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 17:56:47 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 17:56:47 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 17:56:47 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 17:56:47 [信息] 程序退出清理工作完成
