2025-08-03 22:56:20 [信息] AWS自动注册工具启动
2025-08-03 22:56:20 [信息] 程序版本: 1.0.0.0
2025-08-03 22:56:20 [信息] 启动时间: 2025-08-03 22:56:20
2025-08-03 22:56:20 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 22:56:20 [信息] 线程数量已选择: 1
2025-08-03 22:56:20 [信息] 线程数量选择初始化完成
2025-08-03 22:56:20 [信息] 程序初始化完成
2025-08-03 22:56:30 [信息] 浏览器模式切换: AdsPower 模式
2025-08-03 22:56:31 [信息] 浏览器模式切换: 默认 Chrome 模式
2025-08-03 22:56:31 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 22:56:32 [信息] 程序正在退出，开始清理工作...
2025-08-03 22:56:32 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-03 22:56:32 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-03 22:56:32 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-03 22:56:32 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-03 22:56:32 [信息] 程序退出清理工作完成
2025-08-04 13:46:46 [信息] AWS自动注册工具启动
2025-08-04 13:46:46 [信息] 程序版本: 1.0.0.0
2025-08-04 13:46:46 [信息] 启动时间: 2025-08-04 13:46:46
2025-08-04 13:46:46 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 13:46:46 [信息] 线程数量已选择: 1
2025-08-04 13:46:46 [信息] 线程数量选择初始化完成
2025-08-04 13:46:46 [信息] 程序初始化完成
2025-08-04 13:46:48 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 13:46:50 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 13:46:51 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 13:46:51 [信息] 成功加载 10 条数据
2025-08-04 13:46:53 [信息] 线程数量已选择: 3
2025-08-04 13:48:42 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 13:48:42 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 13:48:42 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 10
2025-08-04 13:48:42 [信息] 所有线程已停止并清理
2025-08-04 13:48:42 [信息] 正在初始化多线程服务...
2025-08-04 13:48:42 [信息] 千川手机API服务已初始化
2025-08-04 13:48:42 [信息] 手机号码管理器已初始化，服务商: Qianchuan，将在第一个线程完成第二页后获取手机号码
2025-08-04 13:48:42 [信息] 多线程服务初始化完成
2025-08-04 13:48:42 [信息] 数据分配完成：共10条数据分配给3个线程
2025-08-04 13:48:42 [信息] 线程1分配到4条数据
2025-08-04 13:48:42 [信息] 线程2分配到3条数据
2025-08-04 13:48:42 [信息] 线程3分配到3条数据
2025-08-04 13:48:42 [信息] 屏幕工作区域: 1280x672
2025-08-04 13:48:42 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 13:48:42 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 13:48:42 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 13:48:42 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_012, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=4 GB
2025-08-04 13:48:42 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 13:48:42 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 13:48:42 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 13:48:42 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 13:48:42 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 13:48:42 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 13:48:42 [信息] 屏幕工作区域: 1280x672
2025-08-04 13:48:42 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 13:48:42 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 13:48:42 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 13:48:42 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_004, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=16 GB
2025-08-04 13:48:42 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 13:48:42 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 13:48:42 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 13:48:42 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 13:48:42 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 13:48:42 [信息] 屏幕工作区域: 1280x672
2025-08-04 13:48:42 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 13:48:42 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 13:48:42 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 13:48:42 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_007, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=4 GB
2025-08-04 13:48:42 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 13:48:42 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 13:48:42 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 13:48:42 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 13:48:42 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 13:48:42 [信息] 多线程注册启动成功，共3个线程
2025-08-04 13:48:42 线程1：[信息] 开始启动注册流程
2025-08-04 13:48:42 线程2：[信息] 开始启动注册流程
2025-08-04 13:48:42 线程3：[信息] 开始启动注册流程
2025-08-04 13:48:42 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 13:48:42 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 13:48:42 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 13:48:42 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 13:48:42 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 13:48:42 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 13:48:42 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 13:48:42 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 13:48:42 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 13:48:42 [信息] 多线程管理窗口已初始化
2025-08-04 13:48:42 [信息] UniformGrid列数已更新为: 1
2025-08-04 13:48:42 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 13:48:42 [信息] 多线程管理窗口已打开
2025-08-04 13:48:42 [信息] 多线程注册启动成功，共3个线程
2025-08-04 13:48:45 [信息] UniformGrid列数已更新为: 1
2025-08-04 13:48:45 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 13:48:45 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 13:48:45 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 13:48:45 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-08-04 13:48:45 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 13:48:46 [信息] UniformGrid列数已更新为: 1
2025-08-04 13:48:46 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 13:48:46 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 13:48:46 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 13:48:46 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-04 13:48:46 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 13:48:46 [信息] UniformGrid列数已更新为: 2
2025-08-04 13:48:46 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 13:48:46 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 13:48:46 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 13:48:46 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-04 13:48:46 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 13:48:48 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 13:48:48 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 13:48:48 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 13:48:50 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 13:48:50 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 13:48:50 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-04 13:48:50 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 4核 (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=4 GB
2025-08-04 13:48:50 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 13:48:50 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 13:48:50 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-04 13:48:50 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_004, CPU: 32核 (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器指纹注入: Canvas=canvas_fp_004, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=16 GB
2025-08-04 13:48:50 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 13:48:50 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 13:48:50 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-04 13:48:50 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_012, CPU: 2核 (进度: 0%)
2025-08-04 13:48:50 [信息] 浏览器指纹注入: Canvas=canvas_fp_012, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=4 GB
2025-08-04 13:48:51 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 13:48:52 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 13:48:52 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 13:48:52 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 32
   • 设备内存: 16 GB
   • 平台信息: Win32
   • Do Not Track: system

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5E6F7A8B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_001
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: BC-DE-F0-12-34-56
   • 屏幕分辨率: 1948x1133
   • 可用区域: 1948x1093

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.43
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 13:48:52 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 32    • 设备内存: 16 GB    • 平台信息: Win32    • Do Not Track: system   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5E6F7A8B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_001    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: BC-DE-F0-12-34-56    • 屏幕分辨率: 1948x1133    • 可用区域: 1948x1093   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.43    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 13:48:52 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 13:48:52 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 4 GB
   • 平台信息: Win32
   • Do Not Track: 0

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1C2D3E4F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_006
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: BC-DE-F0-12-34-56
   • 屏幕分辨率: 1991x1002
   • 可用区域: 1991x962

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wimax
   • 电池API支持: True
   • 电池电量: 0.33
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 13:48:52 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 4 GB    • 平台信息: Win32    • Do Not Track: 0   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1C2D3E4F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_006    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: BC-DE-F0-12-34-56    • 屏幕分辨率: 1991x1002    • 可用区域: 1991x962   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wimax    • 电池API支持: True    • 电池电量: 0.33    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 13:48:52 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 13:48:52 线程2：[信息] 浏览器启动成功
2025-08-04 13:48:52 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 13:48:52 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 13:48:52 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 13:48:52 线程3：[信息] 浏览器启动成功
2025-08-04 13:48:52 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 13:48:52 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 13:48:53 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 13:48:53 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 13:48:53 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 13:48:53 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 13:48:53 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 13:48:53 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 13:48:53 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 13:48:53 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 13:48:53 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 13:48:53 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 13:48:53 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 13:48:53 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 2
   • 设备内存: 4 GB
   • 平台信息: Win32
   • Do Not Track: enabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3C4D5E6F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-87EL3RX
   • MAC地址: 11-22-33-44-55-66
   • 屏幕分辨率: 2118x1075
   • 可用区域: 2118x1035

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A3B4C5D6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 4g
   • 电池API支持: True
   • 电池电量: 0.57
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 13:48:53 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 2    • 设备内存: 4 GB    • 平台信息: Win32    • Do Not Track: enabled   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3C4D5E6F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-87EL3RX    • MAC地址: 11-22-33-44-55-66    • 屏幕分辨率: 2118x1075    • 可用区域: 2118x1035   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A3B4C5D6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 4g    • 电池API支持: True    • 电池电量: 0.57    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 13:48:53 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 13:48:53 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 13:48:53 线程1：[信息] 浏览器启动成功
2025-08-04 13:48:53 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 13:48:53 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 13:48:54 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 13:48:54 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 13:48:54 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 13:48:54 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 13:48:54 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 13:48:54 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 13:48:54 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 13:48:54 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 13:48:54 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 13:48:54 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 13:48:54 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 13:48:54 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 13:48:54 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 13:48:54 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 13:48:54 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 13:48:54 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 13:48:54 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 13:48:54 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 13:48:54 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 13:48:54 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 13:48:54 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 13:48:54 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 13:48:54 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 13:49:21 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-04 13:49:21 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 13:49:21 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 13:49:21 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 13:49:21 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 13:49:21 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 13:49:24 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-04 13:49:24 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 13:49:24 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 13:49:24 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 13:49:24 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 13:49:24 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 13:49:24 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 13:49:24 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 13:49:24 [信息] 第一页相关失败，数据保持不动
2025-08-04 13:49:24 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 13:49:24 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 13:49:24 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 13:49:24 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 13:49:24 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 13:49:24 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 13:49:24 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 13:49:24 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 13:49:26 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 13:49:27 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 13:49:27 线程1：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 13:49:27 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 13:49:27 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 13:49:27 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 13:49:27 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 13:49:27 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 13:49:27 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 13:49:27 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 13:49:27 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 13:49:27 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 13:49:27 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 13:49:29 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 13:49:29 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 13:49:29 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 13:49:29 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 13:49:29 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 13:49:29 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 13:49:29 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 13:49:31 线程3：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 13:49:31 线程3：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 13:49:31 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 13:49:31 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 13:49:31 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 13:49:31 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 13:49:31 线程3：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 13:49:31 线程3：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 13:49:31 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 13:49:31 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 13:49:31 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 13:49:31 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 13:49:31 线程3：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 13:49:31 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 13:49:31 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 13:49:31 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 13:49:31 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 13:49:34 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35118 字节 (进度: 100%)
2025-08-04 13:49:34 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35118字节，复杂度符合要求 (进度: 100%)
2025-08-04 13:49:34 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 13:49:34 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 13:49:35 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 13:49:35 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 13:49:35 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 13:49:35 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 13:49:35 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 13:49:37 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 13:49:37 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 13:49:37 线程3：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 13:49:37 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 13:49:37 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 13:49:37 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 13:49:37 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 13:49:38 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35421 字节 (进度: 100%)
2025-08-04 13:49:38 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35421字节，复杂度符合要求 (进度: 100%)
2025-08-04 13:49:38 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 13:49:39 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"bfc35n"},"taskId":"cf18c486-70f6-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 13:49:39 线程1：[信息] [信息] 第一页第1次识别结果: bfc35n → 转换为小写: bfc35n (进度: 100%)
2025-08-04 13:49:39 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 13:49:39 线程1：[信息] [信息] 已填入验证码: bfc35n (进度: 100%)
2025-08-04 13:49:39 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 13:49:41 线程1：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 13:49:41 线程1：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-08-04 13:49:42 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"xddbdx"},"taskId":"d1025f5a-70f6-11f0-9768-ba03bdd70631"} (进度: 100%)
2025-08-04 13:49:42 线程2：[信息] [信息] 第一页第1次识别结果: xddbdx → 转换为小写: xddbdx (进度: 100%)
2025-08-04 13:49:42 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 13:49:42 线程2：[信息] [信息] 已填入验证码: xddbdx (进度: 100%)
2025-08-04 13:49:42 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 13:49:43 线程1：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 13:49:44 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 13:49:44 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 13:49:44 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 13:49:44 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 13:49:44 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 13:49:44 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 13:49:44 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 13:49:44 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 13:49:44 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 13:49:44 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 13:49:44 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 13:49:44 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35218 字节 (进度: 100%)
2025-08-04 13:49:44 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35218字节，复杂度符合要求 (进度: 100%)
2025-08-04 13:49:44 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 13:49:46 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 13:49:46 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 13:49:46
2025-08-04 13:49:46 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31554 字节 (进度: 100%)
2025-08-04 13:49:46 线程1：[信息] [信息] ✅ 图片验证通过：200x71px，31554字节，复杂度符合要求 (进度: 100%)
2025-08-04 13:49:46 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 13:49:46 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"fspsm2"},"taskId":"d3de8e42-70f6-11f0-948a-9ab6db23b9b7"} (进度: 100%)
2025-08-04 13:49:46 线程3：[信息] [信息] 第一页第1次识别结果: fspsm2 → 转换为小写: fspsm2 (进度: 100%)
2025-08-04 13:49:46 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 13:49:46 线程3：[信息] [信息] 已填入验证码: fspsm2 (进度: 100%)
2025-08-04 13:49:47 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 13:49:49 线程3：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 13:49:49 线程3：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 13:49:49 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 13:49:49 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 13:49:49 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 13:49:49 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 13:49:49 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 13:49:49 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 13:49:49 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 13:49:49 线程3：[信息] 已继续
2025-08-04 13:49:49 [信息] 线程3已继续
2025-08-04 13:49:49 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"shnc5p"},"taskId":"d5396d2a-70f6-11f0-b896-029e46bc98e3"} (进度: 100%)
2025-08-04 13:49:49 线程1：[信息] [信息] 第一页第2次识别结果: shnc5p → 转换为小写: shnc5p (进度: 100%)
2025-08-04 13:49:49 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 13:49:49 线程1：[信息] [信息] 已填入验证码: shnc5p (进度: 100%)
2025-08-04 13:49:49 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 13:49:49 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 13:49:49 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 13:49:49
2025-08-04 13:49:51 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 13:49:51 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 13:49:51
2025-08-04 13:49:51 线程1：[信息] [信息] 第一页第2次图形验证码识别成功 (进度: 100%)
2025-08-04 13:49:51 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 13:49:51 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 13:49:51 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 13:49:51 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 13:49:51 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 13:49:51 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 13:49:51 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 13:49:51 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 13:49:51 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 13:49:51 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 13:49:52 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 13:49:52 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 13:49:52
2025-08-04 13:49:53 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 13:49:53 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 13:49:53
2025-08-04 13:49:54 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 13:49:54 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 13:49:54
2025-08-04 13:49:54 [信息] [线程2] 邮箱验证码获取成功: 496061，立即停止重复请求
2025-08-04 13:49:54 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 13:49:54 [信息] [线程2] 已清理响应文件
2025-08-04 13:49:54 线程2：[信息] [信息] 验证码获取成功: 496061，正在自动填入... (进度: 25%)
2025-08-04 13:49:54 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 13:49:54 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 13:49:54 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 13:49:54 [信息] 线程2完成第二页事件已处理
2025-08-04 13:49:54 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-04 13:49:54 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 13:49:54 线程2：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 35%)
2025-08-04 13:49:54 [信息] 开始批量获取3个手机号码，服务商: Qianchuan
2025-08-04 13:49:54 [信息] 千川API：不在初始化时获取手机号码，将在各线程第二页完成后获取
2025-08-04 13:49:54 [信息] 批量获取3个手机号码成功
2025-08-04 13:49:54 线程2：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 48%)
2025-08-04 13:49:54 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 13:49:54 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 13:49:56 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 13:49:56 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 13:49:56
2025-08-04 13:49:57 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 13:49:57 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 13:49:57
2025-08-04 13:49:57 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 48%)
2025-08-04 13:49:57 线程2：[信息] [信息] 开始填写密码信息... (进度: 48%)
2025-08-04 13:49:57 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 13:49:58 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 13:49:58 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 48%)
2025-08-04 13:49:58 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870314208","phoneId":"2099814208884","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5820717,"phoneNo":"4367870314208","projectId":804413,"startTime":"2025-08-04 13:49:58","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"9aa3d6d0-7729-4147-a94f-6b00cafb5f8e"}
2025-08-04 13:49:58 [信息] [千川API] 获取手机号码成功: +4367870314208
2025-08-04 13:49:59 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 48%)
2025-08-04 13:49:59 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 13:49:59 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 13:49:59
2025-08-04 13:49:59 [信息] [线程3] 邮箱验证码获取成功: 245230，立即停止重复请求
2025-08-04 13:49:59 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 13:49:59 [信息] [线程3] 已清理响应文件
2025-08-04 13:49:59 线程3：[信息] [信息] 验证码获取成功: 245230，正在自动填入... (进度: 100%)
2025-08-04 13:49:59 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 13:49:59 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 13:49:59 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 13:49:59 [信息] 线程3完成第二页事件已处理
2025-08-04 13:49:59 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-04 13:49:59 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 13:49:59 线程3：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 100%)
2025-08-04 13:49:59 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 13:49:59 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 13:49:59 线程3：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 100%)
2025-08-04 13:50:02 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 48%)
2025-08-04 13:50:02 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 48%)
2025-08-04 13:50:02 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 48%)
2025-08-04 13:50:02 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 13:50:02 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 13:50:02
2025-08-04 13:50:02 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 13:50:02 线程3：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 13:50:03 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 13:50:03 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 13:50:03 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 13:50:03 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870315607","phoneId":"2100215607469","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5820719,"phoneNo":"4367870315607","projectId":804413,"startTime":"2025-08-04 13:50:02","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"d2326ce9-7abc-4054-9386-17a162a06273"}
2025-08-04 13:50:03 [信息] [千川API] 获取手机号码成功: +4367870315607
2025-08-04 13:50:04 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 13:50:05 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-08-04 13:50:05 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 13:50:05
2025-08-04 13:50:07 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 13:50:07 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 13:50:07 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 13:50:08 [信息] [线程1] 第6次触发邮箱验证码获取...（最多20次）
2025-08-04 13:50:08 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 13:50:08
2025-08-04 13:50:11 [信息] [线程1] 第7次触发邮箱验证码获取...（最多20次）
2025-08-04 13:50:11 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 13:50:11
2025-08-04 13:50:12 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-04 13:50:12 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-04 13:50:13 [信息] [线程1] 邮箱验证码获取成功: 929749，立即停止重复请求
2025-08-04 13:50:13 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 13:50:13 [信息] [线程1] 已清理响应文件
2025-08-04 13:50:13 线程1：[信息] [信息] 验证码获取成功: 929749，正在自动填入... (进度: 25%)
2025-08-04 13:50:13 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 13:50:13 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 13:50:13 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 13:50:13 [信息] 线程1完成第二页事件已处理
2025-08-04 13:50:13 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-04 13:50:13 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 13:50:13 线程1：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 35%)
2025-08-04 13:50:13 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 13:50:13 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 13:50:13 线程1：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 48%)
2025-08-04 13:50:14 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 48%)
2025-08-04 13:50:14 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 48%)
2025-08-04 13:50:16 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870319801","phoneId":"2101619801951","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5820724,"phoneNo":"4367870319801","projectId":804413,"startTime":"2025-08-04 13:50:16","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"7b369ad4-0520-48fa-b97f-a90168eaa6cf"}
2025-08-04 13:50:16 [信息] [千川API] 获取手机号码成功: +4367870319801
2025-08-04 13:50:16 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 48%)
2025-08-04 13:50:16 线程1：[信息] [信息] 开始填写密码信息... (进度: 48%)
2025-08-04 13:50:17 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 13:50:17 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 13:50:17 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 48%)
2025-08-04 13:50:18 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 48%)
2025-08-04 13:50:21 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 48%)
2025-08-04 13:50:21 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 48%)
2025-08-04 13:50:21 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 48%)
2025-08-04 13:50:33 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 48%)
2025-08-04 13:50:33 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 48%)
2025-08-04 13:50:37 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-04 13:50:37 [警告] 线程3未找到分配的手机号码，服务商: Qianchuan
2025-08-04 13:50:37 线程3：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 100%)
2025-08-04 13:50:37 线程3：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 100%)
2025-08-04 13:50:37 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 13:50:37 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 13:50:37 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 13:50:38 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 48%)
2025-08-04 13:50:38 [警告] 线程2未找到分配的手机号码，服务商: Qianchuan
2025-08-04 13:50:38 线程2：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 48%)
2025-08-04 13:50:38 线程2：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 48%)
2025-08-04 13:50:38 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 13:50:38 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 48%)
2025-08-04 13:50:38 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 48%)
2025-08-04 13:50:39 线程3：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 13:50:39 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 13:50:39 线程3：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 13:50:39 线程3：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 13:50:39 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 13:50:40 线程2：[信息] [信息] 已选择国家: Chile (进度: 48%)
2025-08-04 13:50:40 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 48%)
2025-08-04 13:50:40 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-04 13:50:40 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-04 13:50:40 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870313543","phoneId":"2103813543911","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5820728,"phoneNo":"4367870313543","projectId":804413,"startTime":"2025-08-04 13:50:39","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"d22b4733-26fd-4bb5-9b86-77435a54016d"}
2025-08-04 13:50:40 [信息] [千川API] 获取手机号码成功: +4367870313543
2025-08-04 13:50:40 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-04 13:50:41 线程3：[信息] [信息] 已选择国家代码 +43 (进度: 100%)
2025-08-04 13:50:41 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870318403","phoneId":"2103918403368","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5820729,"phoneNo":"4367870318403","projectId":804413,"startTime":"2025-08-04 13:50:41","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"ffad6296-b845-4a1a-829c-2224d9dce4c5"}
2025-08-04 13:50:41 [信息] [千川API] 获取手机号码成功: +4367870318403
2025-08-04 13:50:41 线程3：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 100%)
2025-08-04 13:50:41 线程3：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 100%)
2025-08-04 13:50:41 线程3：[信息] [信息] 千川手机号码获取成功: +4367870315607 (进度: 100%)
2025-08-04 13:50:41 线程3：[信息] [信息] 千川手机号码已自动填入: +4367870315607 (进度: 100%)
2025-08-04 13:50:42 线程2：[信息] [信息] 已选择国家代码 +43 (进度: 48%)
2025-08-04 13:50:42 线程2：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 48%)
2025-08-04 13:50:42 线程2：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 48%)
2025-08-04 13:50:42 线程2：[信息] [信息] 千川手机号码获取成功: +4367870314208 (进度: 48%)
2025-08-04 13:50:42 线程2：[信息] [信息] 千川手机号码已自动填入: +4367870314208 (进度: 48%)
2025-08-04 13:50:42 线程3：[信息] [信息] 使用已保存的手机号码: 67870315607 (进度: 100%)
2025-08-04 13:50:42 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 13:50:43 线程2：[信息] [信息] 使用已保存的手机号码: 67870314208 (进度: 48%)
2025-08-04 13:50:43 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 48%)
2025-08-04 13:50:46 线程3：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 13:50:46 线程3：[信息] [信息] 正在选择月份: February (进度: 100%)
2025-08-04 13:50:46 线程2：[信息] [信息] 进入付款信息页面... (进度: 48%)
2025-08-04 13:50:46 线程3：[信息] [信息] 已选择月份（标准选项）: February (进度: 100%)
2025-08-04 13:50:47 线程3：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-04 13:50:47 线程2：[信息] [信息] 正在选择月份: January (进度: 48%)
2025-08-04 13:50:47 线程2：[信息] [信息] 已选择月份（标准选项）: January (进度: 48%)
2025-08-04 13:50:47 线程3：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 100%)
2025-08-04 13:50:48 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 13:50:48 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 13:50:48 线程3：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 13:50:48 线程3：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 13:50:48 线程2：[信息] [信息] 正在选择年份: 2031 (进度: 48%)
2025-08-04 13:50:48 线程2：[信息] [信息] 已选择年份（标准选项）: 2031 (进度: 48%)
2025-08-04 13:50:49 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 48%)
2025-08-04 13:50:49 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 48%)
2025-08-04 13:50:49 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-04 13:50:49 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-04 13:50:54 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 13:50:55 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-04 13:50:55 线程3：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-04 13:50:55 线程3：[信息] [信息] 已清空并重新填写手机号码: 67870315607 (进度: 100%)
2025-08-04 13:50:55 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 13:50:56 线程2：[信息] [信息] 已选择国家代码: +43 (进度: 48%)
2025-08-04 13:50:56 线程2：[信息] [信息] 已清空并重新填写手机号码: 67870314208 (进度: 48%)
2025-08-04 13:50:56 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 48%)
2025-08-04 13:50:57 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 13:50:57 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 13:50:57 线程3：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 13:50:57 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 13:50:58 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 48%)
2025-08-04 13:50:58 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 13:50:58 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 13:50:58 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 13:51:00 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 48%)
2025-08-04 13:51:00 [警告] 线程1未找到分配的手机号码，服务商: Qianchuan
2025-08-04 13:51:00 线程1：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 48%)
2025-08-04 13:51:00 线程1：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 48%)
2025-08-04 13:51:03 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 13:51:03 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 13:51:03 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 13:51:03 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 13:51:03 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 13:51:05 [信息] [千川API] 响应内容: {"data":null,"msg":"{\"status\":\"ERROR\",\"message\":\"too many requests\"}","status":500,"success":false,"t":"6ed222b0-b424-4e51-ac18-cc1230d5d9f8"}
2025-08-04 13:51:05 [错误] [千川API] 获取手机号码失败: {"status":"ERROR","message":"too many requests"}
2025-08-04 13:51:05 线程1：[信息] [信息] 后台获取手机号码失败: 获取手机号码失败: {"status":"ERROR","message":"too many requests"} (进度: 48%)
2025-08-04 13:51:05 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 48%)
2025-08-04 13:51:05 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 48%)
2025-08-04 13:51:06 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35349 字节 (进度: 100%)
2025-08-04 13:51:06 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，35349字节，复杂度符合要求 (进度: 100%)
2025-08-04 13:51:06 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 13:51:07 线程1：[信息] [信息] 已选择国家: Chile (进度: 48%)
2025-08-04 13:51:07 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 48%)
2025-08-04 13:51:07 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-04 13:51:07 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-04 13:51:07 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-04 13:51:08 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34936 字节 (进度: 100%)
2025-08-04 13:51:08 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34936字节，复杂度符合要求 (进度: 100%)
2025-08-04 13:51:08 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 13:51:08 线程1：[信息] [信息] 已选择国家代码 +43 (进度: 48%)
2025-08-04 13:51:10 线程1：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 48%)
2025-08-04 13:51:10 线程1：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 48%)
2025-08-04 13:51:10 线程1：[信息] [信息] 千川手机号码获取成功: +4367870319801 (进度: 48%)
2025-08-04 13:51:10 线程1：[信息] [信息] 千川手机号码已自动填入: +4367870319801 (进度: 48%)
2025-08-04 13:51:10 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"yz6mhp"},"taskId":"05df2f96-70f7-11f0-9ab0-ba03bdd70631"} (进度: 100%)
2025-08-04 13:51:10 线程3：[信息] [信息] 第六页第1次识别结果: yz6mhp → 转换为小写: yz6mhp (进度: 100%)
2025-08-04 13:51:10 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 13:51:10 线程3：[信息] [信息] 第六页已填入验证码: yz6mhp (进度: 100%)
2025-08-04 13:51:10 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"mrd6tc"},"taskId":"05340e04-70f7-11f0-ae6e-62c5329370b7"} (进度: 100%)
2025-08-04 13:51:11 线程2：[信息] [信息] 第六页第1次识别结果: mrd6tc → 转换为小写: mrd6tc (进度: 100%)
2025-08-04 13:51:11 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 13:51:11 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 13:51:11 线程2：[信息] [信息] 第六页已填入验证码: mrd6tc (进度: 100%)
2025-08-04 13:51:11 线程1：[信息] [信息] 使用已保存的手机号码: 67870319801 (进度: 48%)
2025-08-04 13:51:11 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 13:51:11 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 48%)
2025-08-04 13:51:14 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 13:51:14 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 13:51:14 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 13:51:14 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 13:51:14 线程1：[信息] [信息] 进入付款信息页面... (进度: 48%)
2025-08-04 13:51:15 线程1：[信息] [信息] 正在选择月份: December (进度: 48%)
2025-08-04 13:51:15 线程1：[信息] [信息] 已选择月份（标准选项）: December (进度: 48%)
2025-08-04 13:51:15 线程1：[信息] [信息] 正在选择年份: 2028 (进度: 48%)
2025-08-04 13:51:16 线程1：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 48%)
2025-08-04 13:51:16 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 48%)
2025-08-04 13:51:16 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 48%)
2025-08-04 13:51:16 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-04 13:51:16 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-04 13:51:17 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 13:51:17 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 13:51:20 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 13:51:20 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 13:51:20 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 13:51:20 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 13:51:22 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 13:51:22 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 13:51:22 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 13:51:22 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 13:51:23 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-04 13:51:24 线程1：[信息] [信息] 已选择国家代码: +43 (进度: 48%)
2025-08-04 13:51:24 线程1：[信息] [信息] 已清空并重新填写手机号码: 67870319801 (进度: 48%)
2025-08-04 13:51:24 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 48%)
2025-08-04 13:51:25 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-04 13:51:25 线程3：[信息] [信息] 线程3等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-04 13:51:26 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 48%)
2025-08-04 13:51:26 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 13:51:26 线程1：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 13:51:26 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 13:51:27 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 13:51:27 线程2：[信息] [信息] 线程2等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-04 13:51:29 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 13:51:29 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 13:51:30 线程3：[信息] [信息] 线程3第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-04 13:51:30 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 13:51:30 [信息] [千川API] 获取验证码请求: 4367870315607
2025-08-04 13:51:31 [信息] [千川API] 验证码响应: {"data":{"code":"1557","message":"ok","modle":"[AMAZON] 1557"},"msg":"操作成功","status":200,"success":true,"t":"731dbbaa-1827-41e5-b77a-1f26d92dcad1"}
2025-08-04 13:51:31 [信息] [千川API] 获取验证码成功: 1557
2025-08-04 13:51:31 线程3：[信息] [信息] 线程3千川验证码获取成功: 1557 (进度: 100%)
2025-08-04 13:51:31 线程3：[信息] [信息] 线程3验证码获取成功: 1557，立即填入验证码... (进度: 100%)
2025-08-04 13:51:31 线程3：[信息] [信息] 自动获取验证码异常: Object reference not set to an instance of an object. (进度: 100%)
2025-08-04 13:51:31 线程3：[信息] [信息] 千川API验证码获取超时，直接转为手动模式 (进度: 100%)
2025-08-04 13:51:31 线程3：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 13:51:32 线程2：[信息] [信息] 线程2第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-04 13:51:32 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 13:51:32 [信息] [千川API] 获取验证码请求: 4367870314208
2025-08-04 13:51:33 [信息] [千川API] 验证码响应: {"data":{"code":"3883","message":"ok","modle":"[AMAZON] 3883"},"msg":"操作成功","status":200,"success":true,"t":"02d5fbfc-e4ef-44e1-8af1-cce11751059b"}
2025-08-04 13:51:33 [信息] [千川API] 获取验证码成功: 3883
2025-08-04 13:51:33 线程2：[信息] [信息] 线程2千川验证码获取成功: 3883 (进度: 100%)
2025-08-04 13:51:33 线程2：[信息] [信息] 线程2验证码获取成功: 3883，立即填入验证码... (进度: 100%)
2025-08-04 13:51:33 线程2：[信息] [信息] 自动获取验证码异常: Object reference not set to an instance of an object. (进度: 100%)
2025-08-04 13:51:33 线程2：[信息] [信息] 千川API验证码获取超时，直接转为手动模式 (进度: 100%)
2025-08-04 13:51:33 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 13:51:36 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35226 字节 (进度: 100%)
2025-08-04 13:51:36 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，35226字节，复杂度符合要求 (进度: 100%)
2025-08-04 13:51:36 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 13:51:40 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"tcgfys"},"taskId":"17c6a7f2-70f7-11f0-9768-ba03bdd70631"} (进度: 100%)
2025-08-04 13:51:40 线程1：[信息] [信息] 第六页第1次识别结果: tcgfys → 转换为小写: tcgfys (进度: 100%)
2025-08-04 13:51:40 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 13:51:40 线程1：[信息] [信息] 第六页已填入验证码: tcgfys (进度: 100%)
2025-08-04 13:51:41 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 13:51:45 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 13:51:45 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 13:51:48 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 13:51:51 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 13:51:51 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 13:51:51 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 13:51:51 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6789位 (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 第七页：检测到用户已输入验证码，直接执行手动模式处理流程... (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6789位 (进度: 100%)
2025-08-04 13:51:53 线程2：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-04 13:51:54 线程2：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-04 13:51:55 线程2：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-04 13:51:56 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-04 13:51:56 线程1：[信息] [信息] 线程1等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-04 13:51:59 线程2：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-04 13:51:59 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 13:51:59 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 13:52:00 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 13:52:01 线程1：[信息] [信息] 线程1第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-04 13:52:01 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 13:52:01 [信息] [千川API] 获取验证码请求: 4367870319801
2025-08-04 13:52:01 线程3：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6793位 (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 第七页：检测到用户已输入验证码，直接执行手动模式处理流程... (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 13:52:01 [信息] [千川API] 验证码响应: {"data":{"code":"2293","message":"ok","modle":"[AMAZON] 2293"},"msg":"操作成功","status":200,"success":true,"t":"e5dfcbd9-24fb-47d9-b093-d3e99966ae1e"}
2025-08-04 13:52:01 [信息] [千川API] 获取验证码成功: 2293
2025-08-04 13:52:01 线程1：[信息] [信息] 线程1千川验证码获取成功: 2293 (进度: 100%)
2025-08-04 13:52:01 线程1：[信息] [信息] 线程1验证码获取成功: 2293，立即填入验证码... (进度: 100%)
2025-08-04 13:52:01 线程1：[信息] [信息] 自动获取验证码异常: Object reference not set to an instance of an object. (进度: 100%)
2025-08-04 13:52:01 线程1：[信息] [信息] 千川API验证码获取超时，直接转为手动模式 (进度: 100%)
2025-08-04 13:52:01 线程1：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6793位 (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-04 13:52:01 线程3：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-04 13:52:02 线程3：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-04 13:52:03 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 13:52:03 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 13:52:05 线程3：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-04 13:52:05 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 13:52:06 线程3：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 13:52:07 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 13:52:10 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 13:52:10 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 13:52:11 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 13:52:11 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 13:52:11 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6809位 (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 第七页：检测到用户已输入验证码，直接执行手动模式处理流程... (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6809位 (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-04 13:52:15 线程1：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-04 13:52:17 线程3：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 13:52:17 线程3：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 13:52:17 线程3：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 13:52:18 线程1：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-04 13:52:18 线程1：[信息] [信息] 检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-04 13:52:18 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：7NEF38Ek6Yf ③AWS密码：7Q4JH2sV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-04 13:52:18 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：7NEF38Ek6Yf ③AWS密码：7Q4JH2sV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 13:52:18 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：7NEF38Ek6Yf ③AWS密码：7Q4JH2sV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 13:52:18 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：7NEF38Ek6Yf ③AWS密码：7Q4JH2sV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 13:52:18 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：7NEF38Ek6Yf ③AWS密码：7Q4JH2sV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 13:52:18 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：7NEF38Ek6Yf ③AWS密码：7Q4JH2sV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 13:52:18 线程1：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-04 13:52:18 [信息] 线程1请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-04 13:52:18 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-04 13:52:18 [信息] 线程1失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-04 13:52:18 线程1：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-04 13:52:18 线程1：[信息] [信息] 注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-04 13:52:18 线程1：[信息] 已继续
2025-08-04 13:52:18 [信息] 线程1已继续
2025-08-04 13:52:29 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 13:52:29 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 13:52:30 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 13:52:30 [信息] 成功点击更多按钮
2025-08-04 13:52:31 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 13:52:31 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 13:52:31 [信息] 成功点击账户信息按钮
2025-08-04 13:52:32 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 13:52:32 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 13:52:32 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 13:52:32 [信息] 成功定位到'安全凭证'链接
2025-08-04 13:52:33 线程3：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 13:52:33 线程3：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 13:52:33 线程3：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 13:52:33 [信息] 成功点击更多按钮
2025-08-04 13:52:34 线程3：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 13:52:34 线程3：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 13:52:34 [信息] 成功点击账户信息按钮
2025-08-04 13:52:35 线程3：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 13:52:35 线程3：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 13:52:35 线程3：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 13:52:35 [信息] 成功定位到'安全凭证'链接
2025-08-04 13:52:41 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 13:52:41 [信息] 成功点击'安全凭证'链接
2025-08-04 13:52:41 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 13:52:52 线程3：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 13:52:52 [信息] 成功点击'安全凭证'链接
2025-08-04 13:52:52 线程3：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 13:52:58 线程2：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 13:52:58 线程2：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 13:52:58 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 13:52:58 [信息] 开始创建和复制访问密钥
2025-08-04 13:52:58 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 13:52:58 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 13:52:58 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 13:52:58 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 13:53:07 线程3：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 13:53:07 线程3：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 13:53:07 线程3：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 13:53:07 [信息] 开始创建和复制访问密钥
2025-08-04 13:53:07 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 13:53:07 线程3：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 13:53:07 线程3：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 13:53:07 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 13:53:10 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 13:53:10 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 13:53:12 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 13:53:12 线程2：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 13:53:12 [信息] 使用id属性定位到确认复选框
2025-08-04 13:53:12 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 13:53:12 [信息] 成功勾选确认复选框
2025-08-04 13:53:13 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 13:53:16 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 13:53:16 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 13:53:18 线程3：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 13:53:18 线程3：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 13:53:18 [信息] 使用id属性定位到确认复选框
2025-08-04 13:53:18 线程3：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 13:53:18 [信息] 成功勾选确认复选框
2025-08-04 13:53:19 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 13:53:23 线程2：[信息] [信息] ❌ 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key").First to be visible (进度: 100%)
2025-08-04 13:53:23 [信息] 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key").First to be visible
2025-08-04 13:53:23 线程2：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 13:53:23 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 13:53:23 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：QmPfBu53 ③AWS密码：4jwZUrTQ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 13:53:23 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：QmPfBu53 ③AWS密码：4jwZUrTQ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 13:53:23 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：QmPfBu53 ③AWS密码：4jwZUrTQ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 13:53:23 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：QmPfBu53 ③AWS密码：4jwZUrTQ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 13:53:23 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：QmPfBu53 ③AWS密码：4jwZUrTQ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 13:53:23 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：QmPfBu53 ③AWS密码：4jwZUrTQ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 13:53:23 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 13:53:23 线程2：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 13:53:23 线程2：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 13:53:23 [信息] 注册完成（密钥提取失败）
2025-08-04 13:53:23 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 13:53:23 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 13:53:23 [信息] 已完成数据移除: <EMAIL>
2025-08-04 13:53:23 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 13:53:23 线程2：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 13:53:23 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 13:53:23 线程2：[信息] 已继续
2025-08-04 13:53:23 [信息] 线程2已继续
2025-08-04 13:53:29 线程3：[信息] [信息] ❌ 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key").First to be visible (进度: 100%)
2025-08-04 13:53:29 [信息] 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key").First to be visible
2025-08-04 13:53:29 线程3：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 13:53:29 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 13:53:30 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：4d3r5wB2wFl ③AWS密码：Sb9BoTsv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 13:53:30 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：4d3r5wB2wFl ③AWS密码：Sb9BoTsv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 13:53:30 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：4d3r5wB2wFl ③AWS密码：Sb9BoTsv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 13:53:30 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：4d3r5wB2wFl ③AWS密码：Sb9BoTsv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 13:53:30 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：4d3r5wB2wFl ③AWS密码：Sb9BoTsv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 13:53:30 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：4d3r5wB2wFl ③AWS密码：Sb9BoTsv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 13:53:30 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 13:53:30 线程3：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 13:53:30 线程3：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 13:53:30 [信息] 注册完成（密钥提取失败）
2025-08-04 13:53:30 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 13:53:30 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 13:53:30 [信息] 已完成数据移除: <EMAIL>
2025-08-04 13:53:30 [信息] 检测到多线程处理中数据为0，已重置按钮状态
2025-08-04 13:53:30 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 13:53:30 线程3：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 13:53:30 [信息] 线程3数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 13:53:30 线程3：[信息] 已继续
2025-08-04 13:53:30 [信息] 线程3已继续
2025-08-04 13:56:27 [信息] 多线程窗口引用已清理
2025-08-04 13:56:27 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 13:56:27 [信息] 多线程管理窗口正在关闭
2025-08-04 13:56:28 [信息] 程序正在退出，开始清理工作...
2025-08-04 13:56:28 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 13:56:28 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 13:56:28 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 13:56:28 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 13:56:28 [信息] 程序退出清理工作完成
