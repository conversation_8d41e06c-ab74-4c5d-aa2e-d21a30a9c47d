2025-08-04 15:03:37 [信息] AWS自动注册工具启动
2025-08-04 15:03:37 [信息] 程序版本: 1.0.0.0
2025-08-04 15:03:37 [信息] 启动时间: 2025-08-04 15:03:37
2025-08-04 15:03:37 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 15:03:37 [信息] 线程数量已选择: 1
2025-08-04 15:03:37 [信息] 线程数量选择初始化完成
2025-08-04 15:03:37 [信息] 程序初始化完成
2025-08-04 15:03:39 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 15:03:42 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 15:03:43 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 15:03:44 [信息] 成功加载 5 条数据
2025-08-04 15:03:51 [信息] 线程数量已选择: 3
2025-08-04 15:03:56 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 15:03:56 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 15:03:56 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 5
2025-08-04 15:03:56 [信息] 所有线程已停止并清理
2025-08-04 15:03:56 [信息] 正在初始化多线程服务...
2025-08-04 15:03:56 [信息] 千川手机API服务已初始化
2025-08-04 15:03:56 [信息] 手机号码管理器已初始化，服务商: Qianchuan，将在第一个线程完成第二页后获取手机号码
2025-08-04 15:03:56 [信息] 多线程服务初始化完成
2025-08-04 15:03:56 [信息] 数据分配完成：共5条数据分配给3个线程
2025-08-04 15:03:56 [信息] 线程1分配到2条数据
2025-08-04 15:03:56 [信息] 线程2分配到2条数据
2025-08-04 15:03:56 [信息] 线程3分配到1条数据
2025-08-04 15:03:56 [信息] 屏幕工作区域: 1280x672
2025-08-04 15:03:56 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 15:03:56 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 15:03:56 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:03:56 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=14核, RAM=24 GB
2025-08-04 15:03:56 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 15:03:56 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:03:56 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:03:56 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 15:03:56 [信息] 屏幕工作区域: 1280x672
2025-08-04 15:03:56 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 15:03:56 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 15:03:56 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:03:56 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_006, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=10 GB
2025-08-04 15:03:56 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 15:03:56 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:03:56 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:03:56 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 15:03:56 [信息] 屏幕工作区域: 1280x672
2025-08-04 15:03:56 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 15:03:56 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 15:03:56 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:03:56 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=64 GB
2025-08-04 15:03:56 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 15:03:56 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:03:56 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 15:03:56 [信息] 多线程注册启动成功，共3个线程
2025-08-04 15:03:56 线程2：[信息] 开始启动注册流程
2025-08-04 15:03:56 线程1：[信息] 开始启动注册流程
2025-08-04 15:03:56 线程3：[信息] 开始启动注册流程
2025-08-04 15:03:56 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 15:03:56 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 15:03:56 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 15:03:56 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 15:03:56 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 15:03:56 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 15:03:56 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 15:03:56 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 15:03:56 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 15:03:56 [信息] 多线程管理窗口已初始化
2025-08-04 15:03:56 [信息] UniformGrid列数已更新为: 1
2025-08-04 15:03:56 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 15:03:56 [信息] 多线程管理窗口已打开
2025-08-04 15:03:56 [信息] 多线程注册启动成功，共3个线程
2025-08-04 15:03:59 [信息] UniformGrid列数已更新为: 1
2025-08-04 15:03:59 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 15:03:59 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 15:03:59 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 15:03:59 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 15:03:59 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 15:04:00 [信息] UniformGrid列数已更新为: 1
2025-08-04 15:04:00 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 15:04:00 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 15:04:00 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 15:04:00 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-04 15:04:00 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 15:04:00 [信息] UniformGrid列数已更新为: 2
2025-08-04 15:04:00 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 15:04:00 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 15:04:00 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 15:04:00 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-08-04 15:04:00 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 15:04:02 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 15:04:02 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 15:04:02 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 15:04:04 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 15:04:04 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 15:04:04 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 15:04:04 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:04:04 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-04 15:04:04 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-04 15:04:04 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 14核 (进度: 0%)
2025-08-04 15:04:04 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=14核, RAM=24 GB
2025-08-04 15:04:04 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 15:04:04 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 15:04:04 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 15:04:04 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:04:04 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 15:04:04 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 15:04:04 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 18核 (进度: 0%)
2025-08-04 15:04:04 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=64 GB
2025-08-04 15:04:05 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 15:04:05 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 15:04:05 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 15:04:05 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:04:05 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-04 15:04:05 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-04 15:04:05 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 15:04:05 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_006, CPU: 4核 (进度: 0%)
2025-08-04 15:04:05 [信息] 浏览器指纹注入: Canvas=canvas_fp_006, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=10 GB
2025-08-04 15:04:06 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 15:04:06 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 15:04:07 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 10 GB
   • 平台信息: Win32
   • Do Not Track: manual

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: A1-B2-C3-D4-E5-F6
   • 屏幕分辨率: 1744x1023
   • 可用区域: 1744x983

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: bluetooth
   • 电池API支持: True
   • 电池电量: 0.38
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 15:04:07 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 10 GB    • 平台信息: Win32    • Do Not Track: manual   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: A1-B2-C3-D4-E5-F6    • 屏幕分辨率: 1744x1023    • 可用区域: 1744x983   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: bluetooth    • 电池API支持: True    • 电池电量: 0.38    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 15:04:07 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 18
   • 设备内存: 64 GB
   • 平台信息: Win32
   • Do Not Track: 1

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1C2D3E4F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-G5H6I7J
   • MAC地址: 34-56-78-9A-BC-DE
   • 屏幕分辨率: 1883x1043
   • 可用区域: 1883x1003

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.26
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 15:04:07 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 18    • 设备内存: 64 GB    • 平台信息: Win32    • Do Not Track: 1   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1C2D3E4F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-G5H6I7J    • MAC地址: 34-56-78-9A-BC-DE    • 屏幕分辨率: 1883x1043    • 可用区域: 1883x1003   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.26    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 15:04:07 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 15:04:07 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 14
   • 设备内存: 24 GB
   • 平台信息: Win32
   • Do Not Track: user

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1E2F3A4B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 2080x1099
   • 可用区域: 2080x1059

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A3B4C5D6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: satellite
   • 电池API支持: True
   • 电池电量: 0.33
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 15:04:07 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 14    • 设备内存: 24 GB    • 平台信息: Win32    • Do Not Track: user   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1E2F3A4B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 2080x1099    • 可用区域: 2080x1059   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A3B4C5D6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: satellite    • 电池API支持: True    • 电池电量: 0.33    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 15:04:07 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 15:04:07 线程2：[信息] 浏览器启动成功
2025-08-04 15:04:07 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 15:04:08 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 15:04:08 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 15:04:08 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 15:04:08 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 15:04:08 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 15:04:08 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 15:04:08 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 15:04:08 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 15:04:08 线程1：[信息] 浏览器启动成功
2025-08-04 15:04:08 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 15:04:08 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 15:04:08 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 15:04:08 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 15:04:08 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 15:04:08 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 15:04:08 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 15:04:08 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 15:04:08 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 15:04:08 线程3：[信息] 浏览器启动成功
2025-08-04 15:04:08 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 15:04:09 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 15:04:09 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 15:04:09 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 15:04:09 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 15:04:09 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 15:04:09 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 15:04:09 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 15:04:09 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 15:04:09 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 15:04:12 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 15:04:12 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 15:04:12 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 15:04:12 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 15:04:12 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-04 15:04:12 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 15:04:12 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 15:04:12 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 15:04:12 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 15:04:12 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 15:04:12 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-04 15:04:12 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 15:04:12 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 15:04:12 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 15:04:12 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 15:04:42 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 15:04:42 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 15:04:42 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 15:04:42 [信息] 第一页相关失败，数据保持不动
2025-08-04 15:04:42 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 15:04:42 [信息] 第一页相关失败，数据保持不动
2025-08-04 15:04:42 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 15:04:42 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 15:04:42 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 15:04:42 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 15:04:42 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 15:04:42 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 15:04:42 [信息] 多线程状态已重置
2025-08-04 15:04:42 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 15:04:42 [信息] 多线程状态已重置
2025-08-04 15:04:42 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 15:04:42 [信息] 第一页相关失败，数据保持不动
2025-08-04 15:04:42 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 15:04:42 [信息] 多线程状态已重置
2025-08-04 15:04:42 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 15:04:42 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 15:05:55 [信息] 多线程窗口引用已清理
2025-08-04 15:05:55 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 15:05:55 [信息] 多线程管理窗口正在关闭
2025-08-04 15:05:57 [信息] 程序正在退出，开始清理工作...
2025-08-04 15:05:57 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 15:05:57 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 15:05:57 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 15:05:57 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 15:05:57 [信息] 程序退出清理工作完成
2025-08-04 15:06:02 [信息] AWS自动注册工具启动
2025-08-04 15:06:02 [信息] 程序版本: 1.0.0.0
2025-08-04 15:06:02 [信息] 启动时间: 2025-08-04 15:06:02
2025-08-04 15:06:02 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 15:06:02 [信息] 线程数量已选择: 1
2025-08-04 15:06:02 [信息] 线程数量选择初始化完成
2025-08-04 15:06:02 [信息] 程序初始化完成
2025-08-04 15:06:04 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 15:06:06 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 15:06:07 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 15:06:07 [信息] 成功加载 5 条数据
2025-08-04 15:06:09 [信息] 线程数量已选择: 3
2025-08-04 15:06:13 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 15:06:13 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 15:06:13 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 5
2025-08-04 15:06:13 [信息] 所有线程已停止并清理
2025-08-04 15:06:13 [信息] 正在初始化多线程服务...
2025-08-04 15:06:13 [信息] 千川手机API服务已初始化
2025-08-04 15:06:13 [信息] 手机号码管理器已初始化，服务商: Qianchuan，将在第一个线程完成第二页后获取手机号码
2025-08-04 15:06:13 [信息] 多线程服务初始化完成
2025-08-04 15:06:13 [信息] 数据分配完成：共5条数据分配给3个线程
2025-08-04 15:06:13 [信息] 线程1分配到2条数据
2025-08-04 15:06:13 [信息] 线程2分配到2条数据
2025-08-04 15:06:13 [信息] 线程3分配到1条数据
2025-08-04 15:06:13 [信息] 屏幕工作区域: 1280x672
2025-08-04 15:06:13 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 15:06:13 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 15:06:13 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:06:13 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_005, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=6 GB
2025-08-04 15:06:13 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 15:06:13 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:06:13 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:06:13 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 15:06:13 [信息] 屏幕工作区域: 1280x672
2025-08-04 15:06:13 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 15:06:13 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 15:06:13 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:06:13 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_004, WebGL=ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=8 GB
2025-08-04 15:06:13 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 15:06:13 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:06:13 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:06:13 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 15:06:13 [信息] 屏幕工作区域: 1280x672
2025-08-04 15:06:13 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 15:06:13 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 15:06:13 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:06:13 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_002, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=8 GB
2025-08-04 15:06:13 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 15:06:13 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:06:13 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 15:06:13 [信息] 多线程注册启动成功，共3个线程
2025-08-04 15:06:13 线程1：[信息] 开始启动注册流程
2025-08-04 15:06:13 线程2：[信息] 开始启动注册流程
2025-08-04 15:06:13 线程3：[信息] 开始启动注册流程
2025-08-04 15:06:13 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-04 15:06:13 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 15:06:13 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 15:06:13 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 15:06:13 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 15:06:13 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 15:06:13 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 15:06:13 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 15:06:13 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 15:06:13 [信息] 多线程管理窗口已初始化
2025-08-04 15:06:13 [信息] UniformGrid列数已更新为: 1
2025-08-04 15:06:13 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 15:06:13 [信息] 多线程管理窗口已打开
2025-08-04 15:06:13 [信息] 多线程注册启动成功，共3个线程
2025-08-04 15:06:17 [信息] UniformGrid列数已更新为: 1
2025-08-04 15:06:17 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 15:06:17 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 15:06:17 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 15:06:17 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 15:06:17 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 15:06:18 [信息] UniformGrid列数已更新为: 1
2025-08-04 15:06:18 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 15:06:18 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 15:06:18 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 15:06:18 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 15:06:18 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 15:06:18 [信息] UniformGrid列数已更新为: 2
2025-08-04 15:06:18 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 15:06:18 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 15:06:18 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 15:06:18 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-04 15:06:18 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 15:06:20 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 15:06:20 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 15:06:20 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 15:06:22 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 15:06:22 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:06:22 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-04 15:06:22 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_002, CPU: 4核 (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器指纹注入: Canvas=canvas_fp_002, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=8 GB
2025-08-04 15:06:22 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 15:06:22 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:06:22 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 15:06:22 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 15:06:22 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:06:22 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-04 15:06:22 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_004, CPU: 18核 (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器指纹注入: Canvas=canvas_fp_004, WebGL=ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=8 GB
2025-08-04 15:06:22 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 6核 (进度: 0%)
2025-08-04 15:06:22 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=6 GB
2025-08-04 15:06:23 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 15:06:24 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 15:06:24 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 15:06:24 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 8 GB
   • 平台信息: Win32
   • Do Not Track: enabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1E2F3A4B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-G5H6I7J
   • MAC地址: DE-F0-12-34-56-78
   • 屏幕分辨率: 1733x1090
   • 可用区域: 1733x1050

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.68
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 15:06:24 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 8 GB    • 平台信息: Win32    • Do Not Track: enabled   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1E2F3A4B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-G5H6I7J    • MAC地址: DE-F0-12-34-56-78    • 屏幕分辨率: 1733x1090    • 可用区域: 1733x1050   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.68    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 15:06:24 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 15:06:24 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 15:06:24 线程3：[信息] 浏览器启动成功
2025-08-04 15:06:24 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 15:06:24 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 6
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: disabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7E8F9A0B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_008
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Y9Z0A1B
   • MAC地址: BC-DE-F0-12-34-56
   • 屏幕分辨率: 1891x1034
   • 可用区域: 1891x994

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E5F6A7B8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 4g
   • 电池API支持: True
   • 电池电量: 0.37
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 15:06:24 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 15:06:24 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 6    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: disabled   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7E8F9A0B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_008    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Y9Z0A1B    • MAC地址: BC-DE-F0-12-34-56    • 屏幕分辨率: 1891x1034    • 可用区域: 1891x994   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E5F6A7B8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 4g    • 电池API支持: True    • 电池电量: 0.37    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 15:06:24 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 15:06:24 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 15:06:24 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 15:06:24 线程1：[信息] 浏览器启动成功
2025-08-04 15:06:24 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 15:06:24 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 15:06:24 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 15:06:24 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 15:06:24 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 15:06:25 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 15:06:25 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 15:06:25 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 15:06:25 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 15:06:25 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 15:06:25 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 15:06:25 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 18
   • 设备内存: 8 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3A4B5C6D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: 34-56-78-9A-BC-DE
   • 屏幕分辨率: 1959x1047
   • 可用区域: 1959x1007

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 4g
   • 电池API支持: True
   • 电池电量: 0.74
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 15:06:25 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 18    • 设备内存: 8 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3A4B5C6D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: 34-56-78-9A-BC-DE    • 屏幕分辨率: 1959x1047    • 可用区域: 1959x1007   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 4g    • 电池API支持: True    • 电池电量: 0.74    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 15:06:25 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 15:06:25 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 15:06:25 线程2：[信息] 浏览器启动成功
2025-08-04 15:06:25 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 15:06:25 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 15:06:25 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 15:06:25 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 15:06:25 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 15:06:25 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 15:06:25 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 15:06:25 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 15:06:25 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 15:06:25 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 15:06:25 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 15:06:26 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 15:06:26 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 15:06:27 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 15:06:27 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 15:06:27 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 15:06:27 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 15:06:27 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 15:06:27 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 15:06:27 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 15:06:27 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 15:06:27 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 15:06:27 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 15:06:27 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 15:06:27 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 15:06:57 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 15:06:57 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 15:06:57 [信息] 第一页相关失败，数据保持不动
2025-08-04 15:06:57 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 15:06:57 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 15:06:58 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-04 15:06:58 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 15:06:58 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 15:06:59 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 15:06:59 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 15:06:59 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 15:06:59 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-04 15:06:59 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 15:06:59 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 15:06:59 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 15:06:59 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 15:07:00 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 15:07:02 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 15:07:02 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 15:07:02 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 15:07:02 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 15:07:02 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 15:07:02 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 15:07:03 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 15:07:03 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 15:07:03 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 15:07:03 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 15:07:03 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 15:07:03 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 15:07:04 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 15:07:04 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 15:07:04 线程3：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 15:07:04 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 15:07:04 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 15:07:04 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 15:07:04 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 15:07:05 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 15:07:05 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 15:07:05 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 15:07:05 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 15:07:05 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 15:07:05 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 15:07:05 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 15:07:05 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 15:07:05 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 15:07:05 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 15:07:05 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 15:07:05 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 15:07:05 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 15:07:07 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:07 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:07
2025-08-04 15:07:10 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:10 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:10
2025-08-04 15:07:12 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35219 字节 (进度: 100%)
2025-08-04 15:07:12 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35219字节，复杂度符合要求 (进度: 100%)
2025-08-04 15:07:12 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 15:07:13 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:13 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:13
2025-08-04 15:07:16 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:16 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:16
2025-08-04 15:07:18 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"c73b58"},"taskId":"a83c926a-7101-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 15:07:18 线程3：[信息] [信息] 第一页第1次识别结果: c73b58 → 转换为小写: c73b58 (进度: 100%)
2025-08-04 15:07:18 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 15:07:18 线程3：[信息] [信息] 已填入验证码: c73b58 (进度: 100%)
2025-08-04 15:07:18 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 15:07:19 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:19 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:19
2025-08-04 15:07:20 线程3：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 15:07:20 线程3：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 15:07:20 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 15:07:20 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 15:07:20 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 15:07:20 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 15:07:20 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 15:07:20 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 15:07:20 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 15:07:20 线程3：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 15:07:20 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 15:07:22 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:22 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 15:07:22
2025-08-04 15:07:22 [信息] [线程1] 第6次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:22 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:22
2025-08-04 15:07:25 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:25 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 15:07:25
2025-08-04 15:07:25 [信息] [线程1] 第7次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:25 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:25
2025-08-04 15:07:28 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:28 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 15:07:28
2025-08-04 15:07:28 [信息] [线程1] 第8次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:28 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:28
2025-08-04 15:07:31 [信息] [线程3] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:31 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 15:07:31
2025-08-04 15:07:32 [信息] [线程1] 第9次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:32 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:32
2025-08-04 15:07:34 [信息] [线程3] 邮箱验证码获取成功: 481901，立即停止重复请求
2025-08-04 15:07:34 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 15:07:34 [信息] [线程3] 已清理响应文件
2025-08-04 15:07:34 线程3：[信息] [信息] 验证码获取成功: 481901，正在自动填入... (进度: 25%)
2025-08-04 15:07:34 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 15:07:34 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 15:07:34 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 15:07:34 [信息] 线程3完成第二页事件已处理
2025-08-04 15:07:34 [信息] 线程3完成第二页，开始批量获取手机号码...
2025-08-04 15:07:34 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 15:07:34 [信息] 开始批量获取3个手机号码，服务商: Qianchuan
2025-08-04 15:07:34 [信息] 千川API：不在初始化时获取手机号码，将在各线程第二页完成后获取
2025-08-04 15:07:34 线程3：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 35%)
2025-08-04 15:07:34 [信息] 批量获取3个手机号码成功
2025-08-04 15:07:34 线程3：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 48%)
2025-08-04 15:07:34 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 15:07:34 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 15:07:35 [信息] [线程1] 第10次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:35 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:35
2025-08-04 15:07:37 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 48%)
2025-08-04 15:07:37 线程3：[信息] [信息] 开始填写密码信息... (进度: 48%)
2025-08-04 15:07:37 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 15:07:37 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 15:07:37 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 48%)
2025-08-04 15:07:38 [信息] [线程1] 第11次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:38 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:38
2025-08-04 15:07:38 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 48%)
2025-08-04 15:07:40 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 15:07:40 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 15:07:40 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 15:07:40 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 15:07:40 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 15:07:40 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 15:07:40 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 15:07:40 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 15:07:40 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 15:07:40 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 15:07:40 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 15:07:40 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 15:07:40 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 15:07:40 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 15:07:40 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 15:07:40 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 15:07:40 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 15:07:41 [信息] [线程1] 第12次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:41 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:41
2025-08-04 15:07:41 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 48%)
2025-08-04 15:07:41 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 48%)
2025-08-04 15:07:41 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 48%)
2025-08-04 15:07:43 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 15:07:43 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 15:07:43 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 15:07:43 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 15:07:43 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 15:07:43 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 15:07:44 [信息] [线程1] 第13次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:44 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:44
2025-08-04 15:07:45 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 15:07:46 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 15:07:46 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 15:07:46 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 15:07:46 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 15:07:46 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 15:07:46 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 15:07:47 [信息] [线程1] 第14次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:47 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:47
2025-08-04 15:07:50 [信息] [线程1] 第15次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:50 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:50
2025-08-04 15:07:53 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35151 字节 (进度: 100%)
2025-08-04 15:07:53 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35151字节，复杂度符合要求 (进度: 100%)
2025-08-04 15:07:53 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 15:07:53 [信息] [线程1] 第16次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:53 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:53
2025-08-04 15:07:54 线程3：[信息] [信息] 千川手机号码获取异常: The request was canceled due to the configured HttpClient.Timeout of 20 seconds elapsing. (进度: 48%)
2025-08-04 15:07:54 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 48%)
2025-08-04 15:07:54 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 48%)
2025-08-04 15:07:55 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"b82dwy"},"taskId":"be88ae3c-7101-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 15:07:55 线程2：[信息] [信息] 第一页第1次识别结果: b82dwy → 转换为小写: b82dwy (进度: 100%)
2025-08-04 15:07:55 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 15:07:55 线程2：[信息] [信息] 已填入验证码: b82dwy (进度: 100%)
2025-08-04 15:07:55 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 15:07:56 [信息] [线程1] 第17次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:56 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:56
2025-08-04 15:07:57 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 15:07:57 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 15:07:57 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 15:07:57 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 15:07:57 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 15:07:57 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 15:07:57 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 15:07:57 线程2：[信息] 已继续
2025-08-04 15:07:57 [信息] 线程2已继续
2025-08-04 15:07:57 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 15:07:57 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 15:07:59 [信息] [线程1] 第18次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:59 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:07:59
2025-08-04 15:07:59 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 15:07:59 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 15:07:59
2025-08-04 15:07:59 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 48%)
2025-08-04 15:07:59 [警告] 线程3未找到分配的手机号码，服务商: Qianchuan
2025-08-04 15:07:59 线程3：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 48%)
2025-08-04 15:07:59 线程3：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 48%)
2025-08-04 15:07:59 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 15:08:02 [信息] [线程1] 第19次触发邮箱验证码获取...（最多20次）
2025-08-04 15:08:04 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:08:02
2025-08-04 15:08:04 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 15:08:04 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 15:08:04
2025-08-04 15:08:05 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 48%)
2025-08-04 15:08:05 [信息] [线程1] 第20次触发邮箱验证码获取...（最多20次）
2025-08-04 15:08:05 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 15:08:05
2025-08-04 15:08:05 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 48%)
2025-08-04 15:08:06 [警告] [线程1] 邮箱验证码获取失败（达到最大重试次数），共尝试20次
2025-08-04 15:08:06 [信息] [线程1] 已清理请求文件
2025-08-04 15:08:06 [信息] [线程1] 已清理响应文件
2025-08-04 15:08:06 线程1：[信息] [信息] 邮箱验证码自动获取失败: 邮箱验证码获取失败（达到最大重试次数），共尝试20次 (进度: 18%)
2025-08-04 15:08:06 线程1：[信息] [信息] 🔴 Microsoft获取验证码失败，转为手动模式 (进度: 18%)
2025-08-04 15:08:07 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 15:08:07 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 15:08:07
2025-08-04 15:08:07 线程3：[信息] [信息] 已选择国家: Chile (进度: 48%)
2025-08-04 15:08:07 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 48%)
2025-08-04 15:08:07 线程3：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-04 15:08:07 线程3：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-04 15:08:08 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-04 15:08:08 [信息] [线程2] 邮箱验证码获取成功: 195222，立即停止重复请求
2025-08-04 15:08:08 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 15:08:08 [信息] [线程2] 已清理响应文件
2025-08-04 15:08:08 线程2：[信息] [信息] 验证码获取成功: 195222，正在自动填入... (进度: 100%)
2025-08-04 15:08:08 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 15:08:09 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 15:08:09 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 15:08:09 [信息] 线程2完成第二页事件已处理
2025-08-04 15:08:09 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-04 15:08:09 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 15:08:09 线程2：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 100%)
2025-08-04 15:08:09 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 15:08:09 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 15:08:09 线程2：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 100%)
2025-08-04 15:08:09 线程3：[信息] [信息] 已选择国家代码 +43 (进度: 48%)
2025-08-04 15:08:11 线程3：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 48%)
2025-08-04 15:08:11 线程3：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 48%)
2025-08-04 15:08:11 线程3：[信息] [信息] 千川手机号码获取失败: 获取异常: The request was canceled due to the configured HttpClient.Timeout of 20 seconds elapsing. (进度: 48%)
2025-08-04 15:08:11 线程3：[信息] [信息] 千川手机号码获取失败或超时，转为手动模式 (进度: 48%)
2025-08-04 15:08:11 线程3：[信息] [信息] 开始检查手机号码状态... (进度: 48%)
2025-08-04 15:08:11 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（最多10秒）... (进度: 48%)
2025-08-04 15:08:11 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余9秒）... (进度: 48%)
2025-08-04 15:08:12 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余8秒）... (进度: 48%)
2025-08-04 15:08:12 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 15:08:12 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 15:08:12 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 15:08:12 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 15:08:12 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 15:08:12 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余8秒）... (进度: 48%)
2025-08-04 15:08:13 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余7秒）... (进度: 48%)
2025-08-04 15:08:13 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 15:08:13 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余7秒）... (进度: 48%)
2025-08-04 15:08:14 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余6秒）... (进度: 48%)
2025-08-04 15:08:14 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余6秒）... (进度: 48%)
2025-08-04 15:08:15 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余5秒）... (进度: 48%)
2025-08-04 15:08:15 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余5秒）... (进度: 48%)
2025-08-04 15:08:16 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余4秒）... (进度: 48%)
2025-08-04 15:08:16 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 15:08:16 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 15:08:16 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 15:08:16 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余4秒）... (进度: 48%)
2025-08-04 15:08:17 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余3秒）... (进度: 48%)
2025-08-04 15:08:17 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余3秒）... (进度: 48%)
2025-08-04 15:08:18 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余2秒）... (进度: 48%)
2025-08-04 15:08:18 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余2秒）... (进度: 48%)
2025-08-04 15:08:19 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余1秒）... (进度: 48%)
2025-08-04 15:08:19 线程3：[信息] [信息] 后台获取手机号码异常: The request was canceled due to the configured HttpClient.Timeout of 20 seconds elapsing. (进度: 48%)
2025-08-04 15:08:20 线程3：[信息] [信息] 千川接口：等待手机号码获取完成（剩余1秒）... (进度: 48%)
2025-08-04 15:08:21 线程3：[信息] [信息] 千川接口：10秒内未获取到手机号码，转为手动输入模式 (进度: 48%)
2025-08-04 15:08:21 线程3：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 48%)
2025-08-04 15:08:21 线程3：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 48%)
2025-08-04 15:08:21 线程3：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 48%)
2025-08-04 15:08:21 线程3：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 48%)
2025-08-04 15:08:21 线程3：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 48%)
2025-08-04 15:08:29 线程2：[信息] [信息] 千川手机号码获取异常: The request was canceled due to the configured HttpClient.Timeout of 20 seconds elapsing. (进度: 100%)
2025-08-04 15:08:34 线程2：[信息] [信息] 第3.5页执行失败: Timeout 15000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Choose paid plan" }) to be visible (进度: 100%)
2025-08-04 15:08:40 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForVerification，当前步骤: 2 (进度: 18%)
2025-08-04 15:08:40 线程1：[信息] [信息]  进行智能页面检测... (进度: 18%)
2025-08-04 15:08:40 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 18%)
2025-08-04 15:08:40 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 18%)
2025-08-04 15:08:40 线程1：[信息] [信息] ⚠️ 未找到匹配的页面按钮或链接 (进度: 18%)
2025-08-04 15:08:40 线程1：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 18%)
2025-08-04 15:08:40 线程1：[信息] [信息] 🔬 执行详细页面分析... (进度: 18%)
2025-08-04 15:08:40 线程1：[信息] [信息] 📄 页面URL: https://signin.aws.amazon.com/signup?request_type=register (进度: 18%)
2025-08-04 15:08:40 线程1：[信息] [信息] 📋 页面标题: 线程1 - AWS注册 (进度: 18%)
2025-08-04 15:08:40 线程1：[信息] [信息] 📊 分析结果: 第三页-密码设置(2/3个元素匹配) (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息]  智能检测到当前在第3页 (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] 当前状态: WaitingForVerification, 步骤: 3，尝试智能检测页面... (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] ⚠️ 未找到匹配的页面按钮或链接 (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] 🔬 执行详细页面分析... (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] 📄 页面URL: https://signin.aws.amazon.com/signup?request_type=register (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] 📋 页面标题: 线程1 - AWS注册 (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] 📊 分析结果: 第三页-密码设置(2/3个元素匹配) (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] 智能检测到当前在第3页，继续执行... (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 15:08:40 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 15:08:41 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 15:08:41 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 15:08:41 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 15:08:42 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-04 15:08:42 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 15:08:42 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 15:08:42 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 15:08:42 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Choose paid plan' → 第3页 (进度: 100%)
2025-08-04 15:08:42 线程2：[信息] [信息] 检测到第3.5页（账户类型确认页面），直接点击Choose paid plan按钮... (进度: 100%)
2025-08-04 15:08:42 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 15:08:42 线程2：[信息] [信息] 账户类型确认完成，进入第4页（联系信息页面）... (进度: 100%)
2025-08-04 15:08:45 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 15:08:45 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 15:08:45 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 15:08:45 线程2：[信息] [信息] 检测到注册已暂停或终止，停止后续操作 (进度: 100%)
2025-08-04 15:08:45 线程2：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 100%)
2025-08-04 15:08:45 线程2：[信息] [信息] 🔬 执行详细页面分析... (进度: 100%)
2025-08-04 15:08:45 线程2：[信息] [信息] ❌ 详细分析失败: Execution context was destroyed, most likely because of a navigation (进度: 100%)
2025-08-04 15:08:45 线程2：[信息] [信息] 智能检测到当前在第3页，开始智能处理... (进度: 100%)
2025-08-04 15:08:50 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 15:08:57 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 15:08:57 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 15:09:08 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 15:09:17 线程2：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible (进度: 100%)
2025-08-04 15:09:17 线程2：[信息] 已继续
2025-08-04 15:09:17 [信息] 线程2已继续
2025-08-04 15:09:18 线程3：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 48%)
2025-08-04 15:09:18 线程3：[信息] [信息]  进行智能页面检测... (进度: 48%)
2025-08-04 15:09:18 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 48%)
2025-08-04 15:09:18 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 48%)
2025-08-04 15:09:18 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 48%)
2025-08-04 15:09:18 线程3：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-04 15:09:18 线程3：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-04 15:09:18 线程3：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-08-04 15:09:18 线程3：[信息] [信息] 从页面读取并保存手机号码: 524742071008（不含区号） (进度: 100%)
2025-08-04 15:09:18 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 15:09:21 线程3：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 15:09:22 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-04 15:09:22 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 15:09:22 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 15:09:22 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 15:09:23 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 100%)
2025-08-04 15:09:23 线程2：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-04 15:09:23 线程2：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-04 15:09:23 线程2：[信息] [信息] 智能检测到当前在第4页，开始智能处理... (进度: 100%)
2025-08-04 15:09:23 [警告] 线程2未找到分配的手机号码，服务商: Qianchuan
2025-08-04 15:09:23 线程2：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 100%)
2025-08-04 15:09:23 线程2：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 100%)
2025-08-04 15:09:23 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 15:09:23 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 15:09:23 [警告] 线程1未找到分配的手机号码，服务商: Qianchuan
2025-08-04 15:09:23 线程1：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 38%)
2025-08-04 15:09:23 线程1：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 38%)
2025-08-04 15:09:23 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 15:09:23 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 15:09:24 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 15:09:24 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 15:09:24 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 15:09:25 线程3：[信息] [信息] 正在选择月份: January (进度: 100%)
2025-08-04 15:09:25 线程3：[信息] [信息] 已选择月份（标准选项）: January (进度: 100%)
2025-08-04 15:09:26 线程2：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 15:09:26 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 15:09:26 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 15:09:26 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 15:09:26 线程3：[信息] [信息] 正在选择年份: 2028 (进度: 100%)
2025-08-04 15:09:26 线程1：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 15:09:26 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 15:09:26 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 38%)
2025-08-04 15:09:26 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 38%)
2025-08-04 15:09:26 线程3：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 100%)
2025-08-04 15:09:26 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 15:09:26 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 15:09:27 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 15:09:27 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 15:09:27 线程3：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 15:09:27 线程3：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 15:09:28 线程2：[信息] [信息] 已选择国家代码 +43 (进度: 100%)
2025-08-04 15:09:28 线程1：[信息] [信息] 已选择国家代码 +43 (进度: 38%)
2025-08-04 15:09:29 线程1：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 48%)
2025-08-04 15:09:29 线程1：[信息] [信息] 千川手机号码获取任务未启动 (进度: 48%)
2025-08-04 15:09:29 线程1：[信息] [信息] 千川手机号码获取失败或超时，转为手动模式 (进度: 48%)
2025-08-04 15:09:29 线程1：[信息] [信息] 开始检查手机号码状态... (进度: 48%)
2025-08-04 15:09:29 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（最多10秒）... (进度: 48%)
2025-08-04 15:09:29 线程2：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 100%)
2025-08-04 15:09:29 线程2：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 100%)
2025-08-04 15:09:29 线程2：[信息] [信息] 千川手机号码获取失败: 获取异常: The request was canceled due to the configured HttpClient.Timeout of 20 seconds elapsing. (进度: 100%)
2025-08-04 15:09:29 线程2：[信息] [信息] 千川手机号码获取失败或超时，转为手动模式 (进度: 100%)
2025-08-04 15:09:29 线程2：[信息] [信息] 开始检查手机号码状态... (进度: 100%)
2025-08-04 15:09:29 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（最多10秒）... (进度: 100%)
2025-08-04 15:09:29 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余9秒）... (进度: 48%)
2025-08-04 15:09:29 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余9秒）... (进度: 100%)
2025-08-04 15:09:30 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余8秒）... (进度: 48%)
2025-08-04 15:09:30 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余8秒）... (进度: 100%)
2025-08-04 15:09:30 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余8秒）... (进度: 48%)
2025-08-04 15:09:30 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余8秒）... (进度: 100%)
2025-08-04 15:09:31 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余7秒）... (进度: 48%)
2025-08-04 15:09:31 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余7秒）... (进度: 100%)
2025-08-04 15:09:31 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余7秒）... (进度: 48%)
2025-08-04 15:09:31 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余7秒）... (进度: 100%)
2025-08-04 15:09:32 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余6秒）... (进度: 48%)
2025-08-04 15:09:32 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余6秒）... (进度: 100%)
2025-08-04 15:09:32 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余6秒）... (进度: 48%)
2025-08-04 15:09:32 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余6秒）... (进度: 100%)
2025-08-04 15:09:33 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余5秒）... (进度: 48%)
2025-08-04 15:09:33 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余5秒）... (进度: 100%)
2025-08-04 15:09:33 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余5秒）... (进度: 48%)
2025-08-04 15:09:33 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余5秒）... (进度: 100%)
2025-08-04 15:09:34 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余4秒）... (进度: 48%)
2025-08-04 15:09:34 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余4秒）... (进度: 100%)
2025-08-04 15:09:34 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 15:09:34 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余4秒）... (进度: 48%)
2025-08-04 15:09:34 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余4秒）... (进度: 100%)
2025-08-04 15:09:35 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余3秒）... (进度: 48%)
2025-08-04 15:09:35 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余3秒）... (进度: 100%)
2025-08-04 15:09:35 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余3秒）... (进度: 48%)
2025-08-04 15:09:35 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余3秒）... (进度: 100%)
2025-08-04 15:09:35 线程3：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-04 15:09:35 线程3：[信息] [信息] 已清空并重新填写手机号码: 524742071008 (进度: 100%)
2025-08-04 15:09:36 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 15:09:36 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余2秒）... (进度: 48%)
2025-08-04 15:09:36 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余2秒）... (进度: 100%)
2025-08-04 15:09:36 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余2秒）... (进度: 48%)
2025-08-04 15:09:36 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余2秒）... (进度: 100%)
2025-08-04 15:09:37 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余1秒）... (进度: 48%)
2025-08-04 15:09:37 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余1秒）... (进度: 100%)
2025-08-04 15:09:37 线程1：[信息] [信息] 千川接口：等待手机号码获取完成（剩余1秒）... (进度: 48%)
2025-08-04 15:09:37 线程2：[信息] [信息] 千川接口：等待手机号码获取完成（剩余1秒）... (进度: 100%)
2025-08-04 15:09:38 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 15:09:38 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 15:09:38 线程3：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 15:09:38 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 15:09:39 线程1：[信息] [信息] 千川接口：10秒内未获取到手机号码，转为手动输入模式 (进度: 48%)
2025-08-04 15:09:39 线程1：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 48%)
2025-08-04 15:09:39 线程1：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 48%)
2025-08-04 15:09:39 线程1：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 48%)
2025-08-04 15:09:39 线程1：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 48%)
2025-08-04 15:09:39 线程1：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 48%)
2025-08-04 15:09:39 线程1：[信息] 已继续
2025-08-04 15:09:39 [信息] 线程1已继续
2025-08-04 15:09:39 线程2：[信息] [信息] 千川接口：10秒内未获取到手机号码，转为手动输入模式 (进度: 100%)
2025-08-04 15:09:39 线程2：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 100%)
2025-08-04 15:09:39 线程2：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 100%)
2025-08-04 15:09:39 线程2：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 100%)
2025-08-04 15:09:39 线程2：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 100%)
2025-08-04 15:09:39 线程2：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 100%)
2025-08-04 15:09:39 线程2：[信息] 已继续
2025-08-04 15:09:39 [信息] 线程2已继续
2025-08-04 15:09:41 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 15:09:41 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 15:09:43 线程2：[信息] [信息] 后台获取手机号码异常: The request was canceled due to the configured HttpClient.Timeout of 20 seconds elapsing. (进度: 100%)
2025-08-04 15:09:43 线程1：[信息] [信息] 后台获取手机号码异常: The request was canceled due to the configured HttpClient.Timeout of 20 seconds elapsing. (进度: 48%)
2025-08-04 15:09:44 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 15:09:44 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 15:09:44 线程3：[信息] 已暂停
2025-08-04 15:09:44 [信息] 线程3已暂停
2025-08-04 15:09:44 [信息] 线程3已暂停
2025-08-04 15:09:55 线程3：[信息] [信息] 第六页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-04 15:09:55 线程3：[信息] [信息] 第六页第1次识别异常: 验证码元素等待超时，需要手动处理 (进度: 100%)
2025-08-04 15:09:55 线程3：[信息] [信息] 第六页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-04 15:09:55 线程3：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 15:09:55 线程3：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 15:09:55 线程3：[信息] 已继续
2025-08-04 15:09:55 [信息] 线程3已继续
2025-08-04 15:10:36 线程3：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 第七页手动模式：请手动输入验证码 (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6837位 (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-04 15:10:36 线程3：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-04 15:10:37 线程3：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-04 15:10:40 线程3：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-04 15:10:40 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 15:10:55 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 100%)
2025-08-04 15:10:55 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 15:10:55 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 15:10:55 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 15:10:55 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 100%)
2025-08-04 15:10:55 线程2：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-04 15:10:55 线程2：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-04 15:10:55 线程2：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-08-04 15:10:55 线程2：[信息] [信息] 从页面读取并保存手机号码: 525610573336（不含区号） (进度: 100%)
2025-08-04 15:10:56 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 15:10:59 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 15:11:00 线程2：[信息] [信息] 正在选择月份: March (进度: 100%)
2025-08-04 15:11:01 线程2：[信息] [信息] 已选择月份（标准选项）: March (进度: 100%)
2025-08-04 15:11:02 线程2：[信息] [信息] 正在选择年份: 2030 (进度: 100%)
2025-08-04 15:11:02 线程2：[信息] [信息] 已选择年份（标准选项）: 2030 (进度: 100%)
2025-08-04 15:11:03 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 15:11:03 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 15:11:03 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 15:11:03 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 15:11:09 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 15:11:10 线程3：[信息] [信息] 点击完成注册按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-08-04 15:11:10 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 15:11:11 线程2：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-04 15:11:11 线程2：[信息] [信息] 已清空并重新填写手机号码: 525610573336 (进度: 100%)
2025-08-04 15:11:11 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 15:11:13 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 15:11:13 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 15:11:13 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 15:11:13 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 15:11:13 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 15:11:13 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 15:11:16 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 15:11:16 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 15:11:17 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 15:11:17 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 15:11:17 线程2：[信息] 已暂停
2025-08-04 15:11:17 [信息] 线程2已暂停
2025-08-04 15:11:17 [信息] 线程2已暂停
2025-08-04 15:11:18 线程3：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-08-04 15:11:18 [信息] 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-08-04 15:11:18 线程3：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 15:11:18 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 15:11:18 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：UZ5I0L6PhE ③AWS密码：EYnk2m15 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 15:11:18 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：UZ5I0L6PhE ③AWS密码：EYnk2m15 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:11:18 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：UZ5I0L6PhE ③AWS密码：EYnk2m15 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:11:18 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：UZ5I0L6PhE ③AWS密码：EYnk2m15 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:11:18 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：UZ5I0L6PhE ③AWS密码：EYnk2m15 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:11:18 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：UZ5I0L6PhE ③AWS密码：EYnk2m15 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:11:18 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 15:11:18 线程3：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 15:11:18 线程3：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 15:11:18 [信息] 注册完成（密钥提取失败）
2025-08-04 15:11:18 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 15:11:18 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 15:11:18 [信息] 已完成数据移除: <EMAIL>
2025-08-04 15:11:18 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 15:11:18 线程3：[信息] 已继续
2025-08-04 15:11:18 [信息] 线程3已继续
2025-08-04 15:11:18 线程3：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 15:11:18 [信息] 线程3数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 15:11:29 线程2：[信息] [信息] 第六页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-04 15:11:29 线程2：[信息] [信息] 第六页第1次识别异常: 验证码元素等待超时，需要手动处理 (进度: 100%)
2025-08-04 15:11:29 线程2：[信息] [信息] 第六页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-04 15:11:29 线程2：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 15:11:29 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 15:11:29 线程2：[信息] 已继续
2025-08-04 15:11:29 [信息] 线程2已继续
2025-08-04 15:12:19 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 48%)
2025-08-04 15:12:19 线程1：[信息] [信息]  进行智能页面检测... (进度: 48%)
2025-08-04 15:12:19 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 48%)
2025-08-04 15:12:19 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 48%)
2025-08-04 15:12:19 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 48%)
2025-08-04 15:12:19 线程1：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-04 15:12:19 线程1：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-04 15:12:19 线程1：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-08-04 15:12:21 线程1：[信息] [信息] 从页面读取并保存手机号码: 526642853040（不含区号） (进度: 100%)
2025-08-04 15:12:21 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 15:12:24 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 15:12:26 线程1：[信息] [信息] 正在选择月份: July (进度: 100%)
2025-08-04 15:12:26 线程1：[信息] [信息] 已选择月份（标准选项）: July (进度: 100%)
2025-08-04 15:12:27 线程1：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-04 15:12:27 线程1：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 100%)
2025-08-04 15:12:28 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 15:12:28 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 15:12:28 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 15:12:28 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 第七页手动模式：请手动输入验证码 (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6833位 (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-04 15:12:32 线程2：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-04 15:12:34 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 15:12:35 线程1：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-04 15:12:35 线程1：[信息] [信息] 已清空并重新填写手机号码: 526642853040 (进度: 100%)
2025-08-04 15:12:35 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 15:12:35 线程2：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-04 15:12:35 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 15:12:35 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 15:12:36 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 15:12:36 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 15:12:36 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 15:12:36 线程1：[信息] 已暂停
2025-08-04 15:12:36 [信息] 线程1已暂停
2025-08-04 15:12:36 [信息] 线程1已暂停
2025-08-04 15:12:37 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 15:12:37 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 15:12:37 线程1：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 15:12:37 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 15:12:39 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 15:12:39 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 15:12:40 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 15:12:40 线程1：[信息] [信息] 检测到注册已暂停或终止，停止图形验证码处理 (进度: 100%)
2025-08-04 15:12:40 线程1：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 15:12:40 线程1：[信息] 已继续
2025-08-04 15:12:40 [信息] 线程1已继续
2025-08-04 15:12:50 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 15:12:50 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 15:12:50 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 15:13:10 线程2：[信息] [信息] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程... (进度: 100%)
2025-08-04 15:13:10 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-04 15:13:10 线程2：[信息] [信息] 🔍 智能检测更多按钮... (进度: 100%)
2025-08-04 15:13:10 线程2：[信息] [信息] 🔍 第1次检查更多按钮... (进度: 100%)
2025-08-04 15:13:11 线程2：[信息] [信息] ⚠️ 第1次检查未找到更多按钮 (进度: 100%)
2025-08-04 15:13:11 [信息] 第1次检查未找到更多按钮
2025-08-04 15:13:11 线程2：[信息] [信息] ⏳ 等待3秒后重新检查... (进度: 100%)
2025-08-04 15:13:14 线程2：[信息] [信息] 🔍 第2次检查更多按钮... (进度: 100%)
2025-08-04 15:13:14 线程2：[信息] [信息] ✅ 第2次检查成功找到更多按钮 (进度: 100%)
2025-08-04 15:13:14 [信息] 第2次检查成功找到更多按钮
2025-08-04 15:13:14 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 15:13:14 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 15:13:14 [信息] 成功点击更多按钮
2025-08-04 15:13:15 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 15:13:15 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 15:13:15 [信息] 成功点击账户信息按钮
2025-08-04 15:13:18 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 15:13:18 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 15:13:18 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 15:13:18 [信息] 成功定位到'安全凭证'链接
2025-08-04 15:13:26 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-08-04 15:13:26 线程1：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-08-04 15:13:26 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 15:13:26 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 15:13:26 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 15:13:26 线程1：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 15:13:26 线程1：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-08-04 15:13:26 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 15:13:26 线程1：[信息] [信息] 第七页手动模式：请手动输入验证码 (进度: 100%)
2025-08-04 15:13:26 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 15:13:26 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 15:13:27 线程1：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 15:13:27 线程1：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6913位 (进度: 100%)
2025-08-04 15:13:27 线程1：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-04 15:13:27 线程1：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-04 15:13:27 线程1：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-04 15:13:30 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 15:13:30 [信息] 成功点击'安全凭证'链接
2025-08-04 15:13:30 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 15:13:30 线程1：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-04 15:13:30 线程1：[信息] [信息] 检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-04 15:13:30 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：Mg9Ez4e7x29 ③AWS密码：fI8IlfR4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-04 15:13:30 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：Mg9Ez4e7x29 ③AWS密码：fI8IlfR4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 15:13:30 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：Mg9Ez4e7x29 ③AWS密码：fI8IlfR4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 15:13:30 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：Mg9Ez4e7x29 ③AWS密码：fI8IlfR4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 15:13:30 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：Mg9Ez4e7x29 ③AWS密码：fI8IlfR4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 15:13:30 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：Mg9Ez4e7x29 ③AWS密码：fI8IlfR4 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 15:13:30 线程1：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-04 15:13:30 [信息] 线程1请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-04 15:13:30 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-04 15:13:30 [信息] 线程1失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-04 15:13:30 线程1：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-04 15:13:30 线程1：[信息] [信息] 注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-04 15:13:30 线程1：[信息] 已继续
2025-08-04 15:13:30 [信息] 线程1已继续
2025-08-04 15:13:48 线程2：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 15:13:48 线程2：[信息] [信息] 🔍 开始抓取页面中的下一步按钮元素... (进度: 100%)
2025-08-04 15:13:48 [信息] === 开始抓取下一步按钮元素 ===
2025-08-04 15:13:48 [信息] 选择器 'button' 找到 110 个元素:
2025-08-04 15:13:48 [信息]   元素 1: aria-label='Aceptar todas las cookies' text='Aceptar' class='awsccc-u-btn awsccc-u-btn-primary'
2025-08-04 15:13:48 [信息]   元素 2: aria-label='Continuar sin aceptar' text='Rechazar' class='awsccc-u-btn awsccc-u-btn-secondary'
2025-08-04 15:13:48 [信息]   元素 3: aria-label='Personalizar preferencias de cookies' text='Personalizar' class='awsccc-u-btn awsccc-u-btn-secondary'
2025-08-04 15:13:48 [信息]   元素 4: aria-label='Cancelar la personalización de las preferencias de cookies' text='Cancelar' class='awsccc-u-btn awsccc-u-btn-secondary'
2025-08-04 15:13:48 [信息]   元素 5: aria-label='Guardar preferencias personalizadas de cookies' text='Guardar preferencias' class='awsccc-u-btn awsccc-u-btn-primary'
2025-08-04 15:13:48 [信息] 选择器 'button[data-testid]' 找到 65 个元素:
2025-08-04 15:13:48 [信息]   元素 1: data-testid='aws-services-list-button' class='_nav-dropdown__button_8dc2u_71 _nav-dropdown__button--primary_8dc2u_121 _service_memu_8dc2u_476'
2025-08-04 15:13:48 [信息]   元素 2: data-testid='recentlyVisited' text='Visitados recientemente' class='awsui_tabs-tab-link_14rmt_1hyrc_300 awsui_refresh_14rmt_1hyrc_254 awsui_tabs-tab-active_14rmt_1hyrc_378 awsui_tabs-tab-focused_14rmt_1hyrc_481 awsui_active-tab-header_1acwa_dp0cl_6 awsui_tabs-tab-focusable_14rmt_1hyrc_389'
2025-08-04 15:13:48 [信息]   元素 3: data-testid='favorites' data-awsui-analytics='{"action":"select","detail":{"id":"favorites","label":".awsui_tab-label_1acwa_dp0cl_7","position":"2","originTabId":"recentlyVisited"}}' text='Favoritos' class='awsui_tabs-tab-link_14rmt_1hyrc_300 awsui_refresh_14rmt_1hyrc_254 awsui_tabs-tab-focusable_14rmt_1hyrc_389'
2025-08-04 15:13:48 [信息]   元素 4: data-testid='allApplications' data-awsui-analytics='{"action":"select","detail":{"id":"allApplications","label":".awsui_tab-label_1acwa_dp0cl_7","position":"3","originTabId":"recentlyVisited"}}' text='Todas las aplicaciones' class='awsui_tabs-tab-link_14rmt_1hyrc_300 awsui_refresh_14rmt_1hyrc_254 awsui_tabs-tab-focusable_14rmt_1hyrc_389'
2025-08-04 15:13:48 [信息]   元素 5: data-testid='allServices' data-awsui-analytics='{"action":"select","detail":{"id":"allServices","label":".awsui_tab-label_1acwa_dp0cl_7","position":"4","originTabId":"recentlyVisited"}}' text='Todos los servicios' class='awsui_tabs-tab-link_14rmt_1hyrc_300 awsui_refresh_14rmt_1hyrc_254 awsui_tabs-tab-focusable_14rmt_1hyrc_389'
2025-08-04 15:13:48 [信息] 选择器 'button[aria-label]' 找到 35 个元素:
2025-08-04 15:13:48 [信息]   元素 1: aria-label='Aceptar todas las cookies' text='Aceptar' class='awsccc-u-btn awsccc-u-btn-primary'
2025-08-04 15:13:48 [信息]   元素 2: aria-label='Continuar sin aceptar' text='Rechazar' class='awsccc-u-btn awsccc-u-btn-secondary'
2025-08-04 15:13:49 [信息]   元素 3: aria-label='Personalizar preferencias de cookies' text='Personalizar' class='awsccc-u-btn awsccc-u-btn-secondary'
2025-08-04 15:13:49 [信息]   元素 4: aria-label='Cancelar la personalización de las preferencias de cookies' text='Cancelar' class='awsccc-u-btn awsccc-u-btn-secondary'
2025-08-04 15:13:49 [信息]   元素 5: aria-label='Guardar preferencias personalizadas de cookies' text='Guardar preferencias' class='awsccc-u-btn awsccc-u-btn-primary'
2025-08-04 15:13:49 [信息] 选择器 'button[data-awsui-analytics]' 找到 80 个元素:
2025-08-04 15:13:49 [信息]   元素 1: data-testid='favorites' data-awsui-analytics='{"action":"select","detail":{"id":"favorites","label":".awsui_tab-label_1acwa_dp0cl_7","position":"2","originTabId":"recentlyVisited"}}' text='Favoritos' class='awsui_tabs-tab-link_14rmt_1hyrc_300 awsui_refresh_14rmt_1hyrc_254 awsui_tabs-tab-focusable_14rmt_1hyrc_389'
2025-08-04 15:13:49 [信息]   元素 2: data-testid='allApplications' data-awsui-analytics='{"action":"select","detail":{"id":"allApplications","label":".awsui_tab-label_1acwa_dp0cl_7","position":"3","originTabId":"recentlyVisited"}}' text='Todas las aplicaciones' class='awsui_tabs-tab-link_14rmt_1hyrc_300 awsui_refresh_14rmt_1hyrc_254 awsui_tabs-tab-focusable_14rmt_1hyrc_389'
2025-08-04 15:13:49 [信息]   元素 3: data-testid='allServices' data-awsui-analytics='{"action":"select","detail":{"id":"allServices","label":".awsui_tab-label_1acwa_dp0cl_7","position":"4","originTabId":"recentlyVisited"}}' text='Todos los servicios' class='awsui_tabs-tab-link_14rmt_1hyrc_300 awsui_refresh_14rmt_1hyrc_254 awsui_tabs-tab-focusable_14rmt_1hyrc_389'
2025-08-04 15:13:49 [信息]   元素 4: data-testid='cost_management' data-awsui-analytics='{"action":"select","detail":{"id":"cost_management","label":".awsui_tab-label_1acwa_dp0cl_7","position":"5","originTabId":"recentlyVisited"}}' text='Administración financiera en la nube' class='awsui_tabs-tab-link_14rmt_1hyrc_300 awsui_refresh_14rmt_1hyrc_254 awsui_tabs-tab-focusable_14rmt_1hyrc_389'
2025-08-04 15:13:49 [信息]   元素 5: data-testid='management_tools' data-awsui-analytics='{"action":"select","detail":{"id":"management_tools","label":".awsui_tab-label_1acwa_dp0cl_7","position":"6","originTabId":"recentlyVisited"}}' text='Administración y gobierno' class='awsui_tabs-tab-link_14rmt_1hyrc_300 awsui_refresh_14rmt_1hyrc_254 awsui_tabs-tab-focusable_14rmt_1hyrc_389'
2025-08-04 15:13:49 [信息] 选择器 'button[data-testid*='confirm']' 找到 5 个元素:
2025-08-04 15:13:49 [信息]   元素 1: data-testid='access-key-confirm-deactivate-btn' data-awsui-analytics='{"action":"click","detail":{"label":{"root":"self"}},"component":{"name":"awsui.Button","label":{"root":"self"},"properties":{"variant":"primary","disabled":"false"}}}' text='Desactivar' class='awsui_button_vjswe_b07k1_157 awsui_variant-primary_vjswe_b07k1_231'
2025-08-04 15:13:49 [信息]   元素 2: data-testid='confirm-key-upload' data-awsui-analytics='{"action":"click","detail":{"label":{"root":"self"}},"component":{"name":"awsui.Button","label":{"root":"self"},"properties":{"variant":"primary","disabled":"false"}}}' text='Cargar' class='awsui_button_vjswe_b07k1_157 awsui_variant-primary_vjswe_b07k1_231'
2025-08-04 15:13:49 [信息]   元素 3: data-testid='cloudfront-key-confirm-delete' data-awsui-analytics='{"action":"click","detail":{"label":{"root":"self"}},"component":{"name":"awsui.Button","label":{"root":"self"},"properties":{"variant":"primary","disabled":"false"}}}' text='Eliminar' class='awsui_button_vjswe_b07k1_157 awsui_variant-primary_vjswe_b07k1_231'
2025-08-04 15:13:49 [信息]   元素 4: data-testid='confirm-x509-upload' data-awsui-analytics='{"action":"click","detail":{"label":{"root":"self"}},"component":{"name":"awsui.Button","label":{"root":"self"},"properties":{"variant":"primary","disabled":"false"}}}' text='Cargar' class='awsui_button_vjswe_b07k1_157 awsui_variant-primary_vjswe_b07k1_231'
2025-08-04 15:13:49 [信息]   元素 5: data-testid='x509-confirm-delete-btn' data-awsui-analytics='{"component":{"name":"awsui.Button","label":{"root":"self"},"properties":{"variant":"primary","disabled":"true"}}}' text='Eliminar' class='awsui_button_vjswe_b07k1_157 awsui_variant-primary_vjswe_b07k1_231 awsui_disabled_vjswe_b07k1_224'
2025-08-04 15:13:49 [信息] === 下一步按钮元素抓取完成 ===
2025-08-04 15:13:49 线程2：[信息] [信息] ✅ 下一步按钮元素抓取完成 (进度: 100%)
2025-08-04 15:13:49 线程2：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 15:13:49 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 15:13:49 [信息] 开始创建和复制访问密钥
2025-08-04 15:13:49 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 15:13:49 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 15:13:49 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 15:13:49 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 15:14:00 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 15:14:00 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 15:14:02 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 15:14:02 线程2：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 15:14:02 [信息] 使用id属性定位到确认复选框
2025-08-04 15:14:02 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 15:14:02 [信息] 成功勾选确认复选框
2025-08-04 15:14:03 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 15:14:13 线程2：[信息] [信息] ❌ 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key").First to be visible (进度: 100%)
2025-08-04 15:14:13 [信息] 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key").First to be visible
2025-08-04 15:14:13 线程2：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 15:14:13 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 15:14:13 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：M3d77po25n9 ③AWS密码：E4GvQhCv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 15:14:13 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：M3d77po25n9 ③AWS密码：E4GvQhCv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:14:13 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：M3d77po25n9 ③AWS密码：E4GvQhCv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:14:13 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：M3d77po25n9 ③AWS密码：E4GvQhCv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:14:13 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：M3d77po25n9 ③AWS密码：E4GvQhCv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:14:13 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：M3d77po25n9 ③AWS密码：E4GvQhCv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:14:13 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 15:14:13 线程2：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 15:14:13 线程2：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 15:14:13 [信息] 注册完成（密钥提取失败）
2025-08-04 15:14:13 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 15:14:13 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 15:14:13 [信息] 已完成数据移除: <EMAIL>
2025-08-04 15:14:13 [信息] 检测到多线程处理中数据为0，已重置按钮状态
2025-08-04 15:14:13 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 15:14:13 线程2：[信息] 已继续
2025-08-04 15:14:13 线程2：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 15:14:13 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 15:14:13 [信息] 线程2已继续
2025-08-04 15:51:21 [信息] 多线程窗口引用已清理
2025-08-04 15:51:21 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 15:51:21 [信息] 多线程管理窗口正在关闭
2025-08-04 15:51:23 [信息] 程序正在退出，开始清理工作...
2025-08-04 15:51:23 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 15:51:23 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 15:51:23 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 15:51:23 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 15:51:23 [信息] 程序退出清理工作完成
2025-08-04 15:52:09 [信息] AWS自动注册工具启动
2025-08-04 15:52:09 [信息] 程序版本: 1.0.0.0
2025-08-04 15:52:09 [信息] 启动时间: 2025-08-04 15:52:09
2025-08-04 15:52:09 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 15:52:09 [信息] 线程数量已选择: 1
2025-08-04 15:52:09 [信息] 线程数量选择初始化完成
2025-08-04 15:52:09 [信息] 程序初始化完成
2025-08-04 15:52:13 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 15:52:15 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 15:52:16 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 15:52:16 [信息] 成功加载 2 条数据
2025-08-04 15:52:23 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 15:52:23 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-04 15:52:23 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-04 15:52:23 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-04 15:52:23 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-04 15:52:23 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 15:52:23 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-04 15:52:24 [系统状态] 创建无痕模式上下文...
2025-08-04 15:52:26 [系统状态] 使用默认时区: America/New_York
2025-08-04 15:52:26 [信息] 浏览器时区设置: 使用默认时区 America/New_York
2025-08-04 15:52:27 [系统状态] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 8核
2025-08-04 15:52:27 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=14 GB
2025-08-04 15:52:27 [系统状态] 已创建新的无痕模式上下文和页面
2025-08-04 15:52:29 [系统状态] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 14 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_007
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: BC-DE-F0-12-34-56
   • 屏幕分辨率: 1944x1040
   • 可用区域: 1944x1000

🌍 地区语言信息:
   • 主语言: zh-CN
   • 语言列表: zh-CN,en-US
   • 时区偏移: 300分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.49
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================
2025-08-04 15:52:29 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 14 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_007    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: BC-DE-F0-12-34-56    • 屏幕分辨率: 1944x1040    • 可用区域: 1944x1000   地区语言信息:    • 主语言: zh-CN    • 语言列表: zh-CN,en-US    • 时区偏移: 300分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.49    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 15:52:29 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-08-04 15:52:29 [注册开始] 邮箱: <EMAIL>, 索引: 1/2
2025-08-04 15:52:29 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-04 15:52:29 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-08-04 15:52:29 [系统状态] 在现有窗口中新建标签页...
2025-08-04 15:52:29 [系统状态] 使用现有的浏览器上下文
2025-08-04 15:52:29 [系统状态] 正在新建标签页...
2025-08-04 15:52:29 [系统状态] 已设置页面视口大小为600x400
2025-08-04 15:52:29 [系统状态] 验证无痕Chrome模式状态...
2025-08-04 15:52:29 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-08-04 15:52:29 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb...
2025-08-04 15:52:29 [系统状态] 正在打开AWS注册页面...
2025-08-04 15:52:43 [信息] 程序正在退出，开始清理工作...
2025-08-04 15:52:43 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 15:52:43 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 15:52:43 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 15:52:43 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 15:52:43 [信息] 程序退出清理工作完成
2025-08-04 15:52:46 [信息] AWS自动注册工具启动
2025-08-04 15:52:46 [信息] 程序版本: 1.0.0.0
2025-08-04 15:52:46 [信息] 启动时间: 2025-08-04 15:52:46
2025-08-04 15:52:46 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 15:52:46 [信息] 线程数量已选择: 1
2025-08-04 15:52:46 [信息] 线程数量选择初始化完成
2025-08-04 15:52:46 [信息] 程序初始化完成
2025-08-04 15:52:48 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 15:52:50 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 15:52:50 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 15:52:50 [信息] 成功加载 2 条数据
2025-08-04 15:52:52 [信息] 线程数量已选择: 2
2025-08-04 15:52:56 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 15:52:56 [信息] 开始启动多线程注册，线程数量: 2
2025-08-04 15:52:56 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 2
2025-08-04 15:52:56 [信息] 所有线程已停止并清理
2025-08-04 15:52:56 [信息] 正在初始化多线程服务...
2025-08-04 15:52:56 [信息] 榴莲手机API服务已初始化
2025-08-04 15:52:56 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-04 15:52:56 [信息] 多线程服务初始化完成
2025-08-04 15:52:56 [信息] 数据分配完成：共2条数据分配给2个线程
2025-08-04 15:52:56 [信息] 线程1分配到1条数据
2025-08-04 15:52:56 [信息] 线程2分配到1条数据
2025-08-04 15:52:56 [信息] 屏幕工作区域: 1280x672
2025-08-04 15:52:56 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 15:52:56 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 15:52:56 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:52:56 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_012, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=48 GB
2025-08-04 15:52:56 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 15:52:56 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:52:56 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 15:52:56 [信息] 屏幕工作区域: 1280x672
2025-08-04 15:52:56 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 15:52:56 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 15:52:56 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:52:56 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=64 GB
2025-08-04 15:52:56 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-04 15:52:56 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 15:52:56 [信息] 线程2已创建，窗口位置: (0, 329)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 15:52:56 [信息] 多线程注册启动成功，共2个线程
2025-08-04 15:52:56 线程1：[信息] 开始启动注册流程
2025-08-04 15:52:56 线程2：[信息] 开始启动注册流程
2025-08-04 15:52:56 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 15:52:56 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 15:52:56 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 15:52:56 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 15:52:56 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 15:52:56 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 15:52:56 [信息] 多线程管理窗口已初始化
2025-08-04 15:52:57 [信息] UniformGrid列数已更新为: 1
2025-08-04 15:52:57 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 15:52:57 [信息] 多线程管理窗口已打开
2025-08-04 15:52:57 [信息] 多线程注册启动成功，共2个线程
2025-08-04 15:52:58 [信息] UniformGrid列数已更新为: 1
2025-08-04 15:52:58 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 15:52:58 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 15:52:58 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 15:52:58 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 15:52:58 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 15:52:58 [信息] UniformGrid列数已更新为: 1
2025-08-04 15:52:58 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 15:52:58 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 15:52:58 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 15:52:58 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 15:52:58 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 15:52:59 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 15:52:59 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 15:53:01 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 15:53:01 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 15:53:01 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 15:53:01 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:53:01 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-04 15:53:01 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-04 15:53:01 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 15:53:01 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 15:53:01 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 15:53:01 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 15:53:01 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 15:53:01 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 15:53:01 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 6核 (进度: 0%)
2025-08-04 15:53:01 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=64 GB
2025-08-04 15:53:02 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_012, CPU: 18核 (进度: 0%)
2025-08-04 15:53:02 [信息] 浏览器指纹注入: Canvas=canvas_fp_012, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=48 GB
2025-08-04 15:53:03 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 15:53:03 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 15:53:04 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 18
   • 设备内存: 48 GB
   • 平台信息: Win32
   • Do Not Track: manual

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1A2B3C4D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-I7J8K9L
   • MAC地址: 12-34-56-78-9A-BC
   • 屏幕分辨率: 1943x1061
   • 可用区域: 1943x1021

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E9F0A1B2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: bluetooth
   • 电池API支持: True
   • 电池电量: 0.81
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 15:53:04 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 18    • 设备内存: 48 GB    • 平台信息: Win32    • Do Not Track: manual   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1A2B3C4D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-I7J8K9L    • MAC地址: 12-34-56-78-9A-BC    • 屏幕分辨率: 1943x1061    • 可用区域: 1943x1021   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E9F0A1B2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: bluetooth    • 电池API支持: True    • 电池电量: 0.81    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 15:53:04 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 15:53:04 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 15:53:04 线程1：[信息] 浏览器启动成功
2025-08-04 15:53:04 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 15:53:04 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 15:53:04 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 15:53:04 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 15:53:04 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 15:53:04 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 15:53:04 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 15:53:04 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 6
   • 设备内存: 64 GB
   • 平台信息: Win32
   • Do Not Track: system

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7E8F9A0B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: F0-12-34-56-78-9A
   • 屏幕分辨率: 1939x1168
   • 可用区域: 1939x1128

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A1B2C3D4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 3g
   • 电池API支持: True
   • 电池电量: 0.95
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 15:53:04 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 6    • 设备内存: 64 GB    • 平台信息: Win32    • Do Not Track: system   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7E8F9A0B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: F0-12-34-56-78-9A    • 屏幕分辨率: 1939x1168    • 可用区域: 1939x1128   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A1B2C3D4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 3g    • 电池API支持: True    • 电池电量: 0.95    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 15:53:05 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 15:53:05 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 15:53:05 线程2：[信息] 浏览器启动成功
2025-08-04 15:53:05 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 15:53:05 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 15:53:05 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 15:53:05 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 15:53:05 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 15:53:05 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 15:53:05 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 15:53:05 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 15:53:05 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 15:53:35 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 15:53:35 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 15:53:35 [信息] 第一页相关失败，数据保持不动
2025-08-04 15:53:35 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 15:53:35 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 15:53:36 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-04 15:53:36 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 15:53:36 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 15:53:36 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 15:53:36 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 15:53:36 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 15:53:39 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 15:53:39 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 15:53:39 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 15:53:39 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 15:53:39 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 15:53:39 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 15:53:41 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 15:53:41 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 15:53:41 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 15:53:41 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 15:53:41 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 15:53:41 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 15:53:41 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 15:53:49 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 34934 字节 (进度: 100%)
2025-08-04 15:53:49 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，34934字节，复杂度符合要求 (进度: 100%)
2025-08-04 15:53:49 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 15:53:52 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"nws8s8"},"taskId":"29eb8dc4-7108-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 15:53:52 线程2：[信息] [信息] 第一页第1次识别结果: nws8s8 → 转换为小写: nws8s8 (进度: 100%)
2025-08-04 15:53:52 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 15:53:52 线程2：[信息] [信息] 已填入验证码: nws8s8 (进度: 100%)
2025-08-04 15:53:52 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 15:53:54 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 15:53:54 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 15:53:54 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 15:53:54 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 15:53:54 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 15:53:54 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 15:53:54 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 15:53:54 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 15:53:54 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 15:53:54 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 15:53:54 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 15:53:56 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 15:53:56 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 15:53:56
2025-08-04 15:53:59 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 15:53:59 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 15:53:59
2025-08-04 15:54:02 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 15:54:02 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 15:54:02
2025-08-04 15:54:05 [信息] [线程2] 邮箱验证码获取成功: 933296，立即停止重复请求
2025-08-04 15:54:05 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 15:54:05 [信息] [线程2] 已清理响应文件
2025-08-04 15:54:05 线程2：[信息] [信息] 验证码获取成功: 933296，正在自动填入... (进度: 25%)
2025-08-04 15:54:05 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 15:54:05 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 15:54:05 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 15:54:05 [信息] 线程2完成第二页事件已处理
2025-08-04 15:54:05 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-04 15:54:05 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 15:54:05 [信息] 开始批量获取2个手机号码，服务商: Durian
2025-08-04 15:54:05 [信息] [手机API] 批量获取2个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=2&noblack=0&serial=2&secret_key=null&vip=null
2025-08-04 15:54:08 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 15:54:08 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 15:54:08 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 15:54:08 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 15:54:08 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 15:54:09 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 15:54:11 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+529631295187","+524761267522"]}
2025-08-04 15:54:11 [信息] [手机API] 检测到数组格式，元素数量: 2
2025-08-04 15:54:11 [信息] [手机API] 批量获取成功，获得2个手机号码
2025-08-04 15:54:11 [信息] 线程1分配榴莲手机号码: +529631295187
2025-08-04 15:54:11 [信息] 线程2分配榴莲手机号码: +524761267522
2025-08-04 15:54:11 [信息] 榴莲API批量获取手机号码成功，已分配给2个线程
2025-08-04 15:54:11 [信息] 批量获取2个手机号码成功
2025-08-04 15:54:12 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 15:54:12 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 15:54:12 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 15:54:19 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 15:54:19 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 15:54:43 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 15:54:43 [信息] 线程2获取已分配的榴莲手机号码: +524761267522
2025-08-04 15:54:43 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +524761267522 (进度: 38%)
2025-08-04 15:54:43 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 15:54:43 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 15:54:45 线程2：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 15:54:45 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 15:54:45 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 15:54:45 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 15:54:47 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 15:54:48 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 15:54:48 线程2：[信息] [信息] 已自动获取并填入手机号码: +524761267522 (进度: 38%)
2025-08-04 15:54:49 线程2：[信息] [信息] 使用已获取的手机号码: +524761267522（保存本地号码: +524761267522） (进度: 38%)
2025-08-04 15:54:49 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 15:54:52 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 15:54:53 线程2：[信息] [信息] 正在选择月份: August (进度: 38%)
2025-08-04 15:54:53 线程2：[信息] [信息] 已选择月份（标准选项）: August (进度: 38%)
2025-08-04 15:54:54 线程2：[信息] [信息] 正在选择年份: 2029 (进度: 38%)
2025-08-04 15:54:54 线程2：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 38%)
2025-08-04 15:54:55 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 15:54:55 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 15:54:55 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 15:55:00 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 15:55:01 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 15:55:01 线程2：[信息] [信息] 已清空并重新填写手机号码: +524761267522 (进度: 38%)
2025-08-04 15:55:01 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 15:55:03 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 15:55:04 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 15:55:04 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 15:55:04 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 15:55:04 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 15:55:07 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 15:55:07 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 15:55:15 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35126 字节 (进度: 100%)
2025-08-04 15:55:15 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35126字节，复杂度符合要求 (进度: 100%)
2025-08-04 15:55:15 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 15:55:18 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"y7x77b"},"taskId":"5d35dc34-7108-11f0-b896-029e46bc98e3"} (进度: 100%)
2025-08-04 15:55:18 线程2：[信息] [信息] 第六页第1次识别结果: y7x77b → 转换为小写: y7x77b (进度: 100%)
2025-08-04 15:55:18 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 15:55:18 线程2：[信息] [信息] 第六页已填入验证码: y7x77b (进度: 100%)
2025-08-04 15:55:19 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 15:55:22 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 15:55:22 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 15:55:25 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 15:55:28 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 15:55:28 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 15:55:28 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 15:55:28 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 15:55:33 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 15:55:33 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 15:55:33 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 15:55:33 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 15:55:36 线程2：[信息] [信息] 线程2验证码获取成功: 1502 (进度: 100%)
2025-08-04 15:55:36 [信息] 线程2手机号码已加入释放队列: +524761267522 (原因: 获取验证码成功)
2025-08-04 15:55:36 线程2：[信息] [信息] 线程2验证码获取成功: 1502，立即填入验证码... (进度: 100%)
2025-08-04 15:55:36 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 15:55:36 线程2：[信息] [信息] 线程2已自动填入手机验证码: 1502 (进度: 100%)
2025-08-04 15:55:37 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-04 15:55:37 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 15:55:40 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 15:55:40 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 15:55:41 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 15:55:41 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 15:55:44 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 15:55:44 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 15:55:52 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 15:55:52 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 15:55:52 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 15:55:56 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 15:55:56 [信息] 开始释放1个手机号码
2025-08-04 15:55:56 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 15:55:56 [信息] [手机API] 释放手机号码: +524761267522
2025-08-04 15:55:57 [信息] [手机API] 手机号码释放成功: +524761267522
2025-08-04 15:55:57 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 15:55:57 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 15:56:12 线程2：[信息] [信息] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程... (进度: 100%)
2025-08-04 15:56:12 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-04 15:56:12 线程2：[信息] [信息] 🔍 智能检测更多按钮... (进度: 100%)
2025-08-04 15:56:12 线程2：[信息] [信息] 🔍 第1次检查更多按钮... (进度: 100%)
2025-08-04 15:56:13 线程2：[信息] [信息] ⚠️ 第1次检查未找到更多按钮 (进度: 100%)
2025-08-04 15:56:13 [信息] 第1次检查未找到更多按钮
2025-08-04 15:56:13 线程2：[信息] [信息] ⏳ 等待3秒后重新检查... (进度: 100%)
2025-08-04 15:56:16 线程2：[信息] [信息] 🔍 第2次检查更多按钮... (进度: 100%)
2025-08-04 15:56:16 线程2：[信息] [信息] ✅ 第2次检查成功找到更多按钮 (进度: 100%)
2025-08-04 15:56:16 [信息] 第2次检查成功找到更多按钮
2025-08-04 15:56:16 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 15:56:16 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 15:56:16 [信息] 成功点击更多按钮
2025-08-04 15:56:17 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 15:56:17 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 15:56:17 [信息] 成功点击账户信息按钮
2025-08-04 15:56:18 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 15:56:18 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 15:56:18 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 15:56:18 [信息] 成功定位到'安全凭证'链接
2025-08-04 15:56:32 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 15:56:32 [信息] 成功点击'安全凭证'链接
2025-08-04 15:56:32 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 15:56:56 线程2：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 15:56:56 线程2：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 15:56:56 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 15:56:56 [信息] 开始创建和复制访问密钥
2025-08-04 15:56:56 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 15:56:56 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 15:56:56 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 15:56:56 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 15:56:56 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 15:56:56 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 15:56:58 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 15:56:59 线程2：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 15:56:59 [信息] 使用id属性定位到确认复选框
2025-08-04 15:56:59 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 15:56:59 [信息] 成功勾选确认复选框
2025-08-04 15:57:00 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 15:57:10 线程2：[信息] [信息] ❌ 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key").First to be visible (进度: 100%)
2025-08-04 15:57:10 [信息] 创建和复制访问密钥失败: Timeout 10000ms exceeded.
Call log:
  - waiting for GetByTestId("create-access-key").First to be visible
2025-08-04 15:57:10 线程2：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 15:57:10 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 15:57:10 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：mcPsPB89 ③AWS密码：Ume1yUzm ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 15:57:10 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：mcPsPB89 ③AWS密码：Ume1yUzm ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:57:10 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：mcPsPB89 ③AWS密码：Ume1yUzm ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:57:10 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：mcPsPB89 ③AWS密码：Ume1yUzm ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:57:10 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：mcPsPB89 ③AWS密码：Ume1yUzm ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:57:10 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：mcPsPB89 ③AWS密码：Ume1yUzm ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 15:57:10 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 15:57:10 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 15:57:10 [信息] 多线程状态已重置
2025-08-04 15:57:10 线程2：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 15:57:10 线程2：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 15:57:10 [信息] 注册完成（密钥提取失败）
2025-08-04 15:57:10 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 15:57:10 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 15:57:10 [信息] 已完成数据移除: <EMAIL>
2025-08-04 15:57:10 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 15:57:10 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 15:57:10 [信息] 多线程状态已重置
2025-08-04 15:57:10 线程2：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 15:57:10 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 15:57:10 [信息] 多线程状态已重置
2025-08-04 15:57:10 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 16:00:38 [信息] 多线程窗口引用已清理
2025-08-04 16:00:38 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 16:00:38 [信息] 多线程管理窗口正在关闭
2025-08-04 16:00:39 [信息] 程序正在退出，开始清理工作...
2025-08-04 16:00:39 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 16:00:39 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 16:00:39 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 16:00:39 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 16:00:39 [信息] 程序退出清理工作完成
